﻿<!DOCTYPE html>
<html class="dark">

<!-- Mirrored from heovl.fit/videos/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Jun 2025 20:04:37 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
    <title>Úp mặt vào hai bầu vú khủng của nữ giáo viên làm cặc em cương khó chịu - Sex Hay - HeoVL</title>
<meta name="description" content="Nữ giáo viên dâm dục <PERSON>ri <PERSON> cho em học sinh úp mặt và nhào nặn cặp vú khủng của mình rồi cùng em sung sướng." />
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

<link rel="canonical" href="https://heovl.fit/videos/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu" />

<link rel="shortcut icon" href="https://heovl.fit/resize/50/2024/05/09/3aecc32e86cf3a79a98ed9f567354ab1fdd5d5355ddefdbb24855553b519a396.png" type="image/x-icon">

<!-- tacolo verify code -->
<meta name="tlsdk" content="5ebe4073b7ba4501ab5457eac0133266">


<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-GR0GKQ8JBK');
</script>






<!-- Common -->
<script>
window.setCookie = function(n, t, r) {
                    var e = new Date;
                    e.setTime(e.getTime() + 60 * r * 1e3);
                    var u = "expires=" + e.toUTCString();
                    document.cookie = n + "=" + t + ";" + u + ";path=/"
                }; 

window.getCookie = function(n) {
                    for (var t = n + "=", r = decodeURIComponent(document.cookie).split(";"), e = 0; e < r.length; e++) {
                        for (var u = r[e];
                            " " == u.charAt(0);) u = u.substring(1);
                        if (0 == u.indexOf(t)) return u.substring(t.length, u.length)
                    }
                    return ""
                }
</script>

<meta property="og:title" content="Úp mặt vào hai bầu vú khủng của nữ giáo viên làm cặc em cương khó chịu - Sex Hay - HeoVL"/>
<meta property="og:description" content="Nữ giáo viên dâm dục Anri Okita cho em học sinh úp mặt và nhào nặn cặp vú khủng của mình rồi cùng em sung sướng."/>
<meta property="og:url" content="https://heovl.fit/videos/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu"/>
<meta property="og:site_name" content="HeoVL"/>
    <meta property="og:image" content="https://heovl.fit/resize/600/2023/01/4231bc253295107b868861ff52b803791194bb09.jpg"/>
<meta property="og:image:width" content="600"/>

<meta name="twitter:card" content="summary"/>
<meta name="twitter:description" content="Nữ giáo viên dâm dục Anri Okita cho em học sinh úp mặt và nhào nặn cặp vú khủng của mình rồi cùng em sung sướng."/>
<meta name="twitter:title" content="Úp mặt vào hai bầu vú khủng của nữ giáo viên làm cặc em cương khó chịu - Sex Hay - HeoVL"/>
<link rel="stylesheet" href="https://heovl.fit/build/assets/app-Cb5tGUTM.css" />

<link href="https://heovl.fit/assets/hvl/theme.css?p=22" rel="stylesheet">
</head>
<body class="antialiased text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-900">

    <div class="absolute z-20 top-0 inset-x-0 flex justify-center overflow-hidden pointer-events-none" style="">
    <div class="w-[108rem] flex-none flex justify-end">
        <picture>
            <source srcset="https://heovl.fit/assets/hvl/img/background/light.avif?v=2" type="image/avif">
            <img src="https://heovl.fit/assets/hvl/img/background/light.png?v=2" alt="" class="w-[71.75rem] flex-none max-w-none dark:hidden">
        </picture>
        <picture>
            <source srcset="https://heovl.fit/assets/hvl/img/background/dark.avif?v=2" type="image/avif">
            <img src="https://heovl.fit/assets/hvl/img/background/dark.png?v=2" alt="" class="w-[90rem] flex-none max-w-none hidden dark:block">
        </picture>
    </div>
</div>

    <!-- Navbar -->
    

<nav id="navbar" x-data="{ mobileMenuShow: false }">
    <div class="max-w-screen-2xl mx-auto px-3">
        <div class="flex justify-between h-16">
            <div class="flex">
                <div class="-ml-2 mr-2 flex items-center md:hidden">
                    <!-- Mobile menu button -->
                    <button type="button" class="navbar__mobile-button" aria-controls="mobile-menu" aria-expanded="false" x-on:click="mobileMenuShow = !mobileMenuShow">
                        <span class="sr-only">Open main menu</span>
                        <!--
                          Icon when menu is closed.

                          Heroicon name: outline/menu

                          Menu open: "hidden", Menu closed: "block"
                        -->
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <!--
                          Icon when menu is open.

                          Heroicon name: outline/x

                          Menu open: "block", Menu closed: "hidden"
                        -->
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="flex-shrink-0 flex items-center">
                    <a href="https://heovl.fit/" class="text-2xl font-bold dark:text-gray-300">
                        HeoVL
                    </a>
                </div>
                <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
                    <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->

                                                                                        <a href="https://heovl.fit/actresses" title="Diễn viên" class="navbar__link">Diễn viên</a>
                                                <a href="https://heovl.fit/trang/the-loai" title="Thể loại" class="navbar__link">Thể loại</a>
                                                <a href="https://heovl.fit/categories/viet-nam" title="Việt Nam" class="navbar__link">Việt Nam</a>
                                                <a href="https://heovl.fit/categories/vietsub" title="Vietsub" class="navbar__link">Vietsub</a>
                                                <a href="https://heovl.fit/categories/trung-quoc" title="Trung Quốc" class="navbar__link">Trung Quốc</a>
                                                <a href="https://heovl.fit/categories/au-my" title="Âu - Mỹ" class="navbar__link">Âu - Mỹ</a>
                                                <a href="https://heovl.fit/categories/khong-che" title="Không Che" class="navbar__link">Không Che</a>
                                                <a href="https://heovl.fit/categories/jav-hd" title="JAV HD" class="navbar__link">JAV HD</a>
                                                <a href="https://heovl.fit/categories/gai-xinh" title="Gái Xinh" class="navbar__link">Gái Xinh</a>
                                                <a href="https://heovl.fit/categories/nghiep-du" title="Nghiệp Dư" class="navbar__link">Nghiệp Dư</a>
                                                <a href="https://heovl.fit/tag/xvideos" title="Xvideos" class="navbar__link">Xvideos</a>
                                                <a href="https://heovl.fit/categories/xnxx" title="Xnxx" class="navbar__link">Xnxx</a>
                                            
                </div>
            </div>
            <div class="flex items-center">
                <div class="md:ml-4 md:flex-shrink-0 md:flex md:items-center">

                    <!-- Search -->
                    <a href="https://heovl.fit/search" class="navbar__link-search">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </a>

                    <button onclick="toggleDarkMode()" class="navbar__toggle-dark-mode-button">
                        <!-- Moon -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="dark:hidden w-5 h-5 transform -rotate-90"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>

                        <!-- Sun -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="dark:block hidden w-5 h-5"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
                    </button>

                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div x-show="mobileMenuShow" x-transition.duration.700ms id="mobile-menu" style="display: none">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->

                                                        <a href="https://heovl.fit/actresses" title="Diễn viên" class="navbar__mobile-link">Diễn viên</a>
                                <a href="https://heovl.fit/trang/the-loai" title="Thể loại" class="navbar__mobile-link">Thể loại</a>
                                <a href="https://heovl.fit/categories/viet-nam" title="Việt Nam" class="navbar__mobile-link">Việt Nam</a>
                                <a href="https://heovl.fit/categories/vietsub" title="Vietsub" class="navbar__mobile-link">Vietsub</a>
                                <a href="https://heovl.fit/categories/trung-quoc" title="Trung Quốc" class="navbar__mobile-link">Trung Quốc</a>
                                <a href="https://heovl.fit/categories/au-my" title="Âu - Mỹ" class="navbar__mobile-link">Âu - Mỹ</a>
                                <a href="https://heovl.fit/categories/khong-che" title="Không Che" class="navbar__mobile-link">Không Che</a>
                                <a href="https://heovl.fit/categories/jav-hd" title="JAV HD" class="navbar__mobile-link">JAV HD</a>
                                <a href="https://heovl.fit/categories/gai-xinh" title="Gái Xinh" class="navbar__mobile-link">Gái Xinh</a>
                                <a href="https://heovl.fit/categories/nghiep-du" title="Nghiệp Dư" class="navbar__mobile-link">Nghiệp Dư</a>
                                <a href="https://heovl.fit/tag/xvideos" title="Xvideos" class="navbar__mobile-link">Xvideos</a>
                                <a href="https://heovl.fit/categories/xnxx" title="Xnxx" class="navbar__mobile-link">Xnxx</a>
                                    </div>
    </div>
</nav>

<script>
function toggleDarkMode() {
    setDarkMode(window.localStorage.getItem('isDarkMode') === 'yes' ? 'no' : 'yes');
    showDarkMode();
}

function toggleUserProfile() {
    const profileMenu = $('[aria-labelledby="user-menu-button"]');
    if (profileMenu.hasClass('opacity-0')) {
        profileMenu.removeClass('transform opacity-0 scale-95');
        profileMenu.addClass('transform opacity-100 scale-100');
    } else {
        profileMenu.removeClass('transform opacity-100 scale-100');
        profileMenu.addClass('transform opacity-0 scale-95');
    }
}

function setDarkMode(is) {
    window.localStorage.setItem('isDarkMode', is);
}

function showDarkMode() {
    let isDarkMode = window.localStorage.getItem('isDarkMode');
    if (!isDarkMode) {
        setDarkMode('yes');
        isDarkMode = 'yes';
    }

    if (isDarkMode === 'yes') {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

showDarkMode();

// Collapse navbar
document.addEventListener('DOMContentLoaded', () => {
    let latestScrollPosition = 0
    let scrollDirection = null
    let showNavbar = true
    let navbarVisibilityHandler = null
    const navbarElement = document.getElementById('navbar')
    const navbarInitialHeight = navbarElement.offsetHeight

    window.addEventListener('scroll', () => {

        // Ignore if height !== initial height
        if (navbarElement.offsetHeight !== navbarInitialHeight) {
            return
        }

        const currentPosition = window.pageYOffset
        const positionDiff = Math.abs(currentPosition - latestScrollPosition)
        if (positionDiff < 10) {
            return
        }

        if (currentPosition > latestScrollPosition) {
            scrollDirection = 'down'
        } else {
            scrollDirection = 'up'
        }
        latestScrollPosition = currentPosition

        if (
            currentPosition > 50
            && ((scrollDirection === 'down' && !showNavbar)
                || (scrollDirection === 'up' && showNavbar))
        ) {
            return
        }

        if (navbarVisibilityHandler) {
            clearTimeout(navbarVisibilityHandler)
        }

        navbarVisibilityHandler = setTimeout(() => {

            if (scrollDirection === 'up' || currentPosition < 50) {
                navbarElement.style.transform = 'initial'
                showNavbar = true
            } else {
                navbarElement.style.transform = 'translateY(-4rem)'
                showNavbar = false
            }
        }, 10)
    })
})
</script>

    <div class="container mx-auto max-w-screen-2xl mt-3 px-3">
        <div class="md:grid lg:grid-cols-4 md:grid-cols-3 gap-4">

            <!-- 2/3 -->
            <div class="lg:col-span-3 md:col-span-2">
                
                                <div id="detail-page">

        <!-- Breadcrumb -->
                    <nav aria-label="Breadcrumb" class="flex">
    <ol class="breadcrumb">
        <li class="breadcrumb__item">
            <div class="flex items-center">
                <a href="https://heovl.fit/" class="breadcrumb__item__link">
                    <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                </a>
            </div>
        </li>

                <li class="breadcrumb__item">
            <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <a href="https://heovl.fit/categories/bu-lon" class="breadcrumb__item__link">
                    Bú Lồn
                </a>
            </div>
        </li>
            </ol>
</nav>
                  
        <!-- Video player -->
        <div class="mt-3">
                




    <div id="player-wrapper" class=" video-player video-player--x">
            </div>

    
            <div class="media-wrapper">
                        
                                <div class="file-wrapper">
            
                        
                        <div class="cdn-selector-wrapper">
                                                                            <button
                    class="button video-player__cdn-selector-button cdn-selector-button--inactive set-player-source"
                    data-media-title="Default"
                    data-cdn-name="StreamQQ Plan VIP"
                    data-active="false"
                    data-source="https://e.streamqq.com/videos/661ddd818e165c9a8c02fe8a/play?event_id=player-wrapper&amp;adTag=https%3A%2F%2Fvast.freeplayer.click%2Fhvl"
                >
                    Server 1
                </button>
                                        </div>

        </div>
        
    </div>
    
    <script>
    (function () {
        const playerWrapper = document.getElementById('player-wrapper')
        const message = ``
        function setPlayerSource(url) {
            document.querySelectorAll('.set-player-source').forEach(e => e.classList.remove('video-player__cdn-selector-button--active') || e.classList.add('cdn-selector-button--inactive'))
            const activeBtn = document.querySelector('.set-player-source[data-source="' + url + '"]')
            activeBtn.setAttribute('data-active', 'true')
            activeBtn.classList.add('video-player__cdn-selector-button--active')
            activeBtn.classList.remove('cdn-selector-button--inactive')

            if (!url && message) {
                playerWrapper.innerHTML = message
                return
            }

            playerWrapper.innerHTML = `<iframe src="` + url + `" style="border: 0; width: 100%; height: 100%;" frameborder="0" scrolling="0" allowfullscreen></iframe>`
        }

        document.querySelectorAll('.set-player-source').forEach(e => e.addEventListener('click', e => setPlayerSource(e.target.getAttribute('data-source'))))
        const firstSourceElement = document.querySelector('.set-player-source')
        if (firstSourceElement) {
            firstSourceElement.click()
        } else {
            playerWrapper.innerHTML = message
        }
    })()
    </script>

        </div>
        
        <!-- Under video players ads -->
        <style>
	.responsive-container {
		height: 156px;
	}

	@media (max-width: 768px) {
		.responsive-container {
			height: 121px;
		}
	}
</style>

<div id="underPlayerAdsContainer" class="responsive-container"></div>
        
        <!-- Heading -->
        <div class="detail-page__heading">

    <!-- Video title -->
    <div class="pt-3 lg:col-span-2">
        <h1 class="heading-1 mb-0">Úp mặt vào hai bầu vú khủng của nữ giáo viên làm cặc em cương khó chịu</h1>
    </div>

    <!-- Statistics -->
    <div class="mt-4">
        <div class="grid grid-cols-2">
            <div class="pt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <small class="inline text-sm">48915</small>
            </div>
            <div class="pt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                </svg>
                <small class="inline text-sm">0</small>
            </div>
        </div>
    </div>
</div>
        <!-- Information -->
        <div class="detail-page__information mb-5">
    
    <!-- Relations -->
    <div class="">

        <!-- Content -->
                <article class="detail-page__information__content">Nữ giáo viên dâm dục Anri Okita cho em học sinh úp mặt và nhào nặn cặp vú khủng của mình rồi cùng em sung sướng.Quốc Gia - Châu Lục: Sex nhật.Mặt + Body: Địt vú to, da Đẹp, mông Đẹp, mông to, sexy, vú đẹp.Tư Thế: đá hột le, cưỡi ngựa, Địt vú, bú vú, bú cặc, dùng tay kích dục, sóc lọ - móc lồn, bú liếm, hôn, Ôm nhau.Chất lượng phim: Jav 1080.Nhập Vai: Nữ giáo viên.Kiểu Sex: Xuất tinh, Ướt Át, nuốt tinh.Trang Phục: Váy ngắn, mặc khiêu dâm, khỏa thân.Số Người: Cặp Đôi nam nữ.Nguồn Phim: Spankbang.</article>
        
        <!-- Cats -->
                    <div class="featured-list">
    <!-- Desktop view -->
    <div class="featured-list__desktop">

                <h3 class="featured-list__desktop__heading">
            Thể loại
        </h3>
        
        <ul role="list" class="featured-list__desktop__list">
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/categories/bu-lon" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/04/23/cd571eca24c8008e28f84378cbb8c6d9ddcb070f10b90bd5d49f5f0ad5f6cd86.jpg" alt="Bú Lồn">
                </a>
                <a href="https://heovl.fit/categories/bu-lon" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Bú Lồn
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            4023 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/categories/jav-hd" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/04/23/a664ea4a5581c7d5aa0bc951df71550111dbfd70aaebe4ebfb3f5e292150028c.jpg" alt="JAV HD">
                </a>
                <a href="https://heovl.fit/categories/jav-hd" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            JAV HD
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            6122 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/categories/nhat-ban" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/04/23/84bdef617c55c3c03d4897e14f355153e5e9b82bd2998357dee89ac90a70fa76.jpg" alt="Nhật Bản">
                </a>
                <a href="https://heovl.fit/categories/nhat-ban" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Nhật Bản
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            4008 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/categories/phimheovip" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/04/23/bb03215e618b4bca40978a60783a35a6764b43abf5677ab3f064b00ca07b15b6.jpg" alt="Phimheovip">
                </a>
                <a href="https://heovl.fit/categories/phimheovip" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Phimheovip
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            7059 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/categories/vu-to" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/04/23/c8bdc0123e07bfe7fd60d68bcc1468a4c2a7ec0505d8ca25c8c073c402e673d8.jpg" alt="Vú To">
                </a>
                <a href="https://heovl.fit/categories/vu-to" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Vú To
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            3330 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/trang/the-loai" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Tất cả thể loại">
                </a>
                <a href="https://heovl.fit/trang/the-loai" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Tất cả thể loại
                        </h3>
                                            </div>
                </a>
            </li>
            
                    </ul>
    </div>

    <!-- Mobile view -->
    </div>
        
        <!-- Tag groups -->
                        <div class="mt-3">
            <div class="featured-list">
    <!-- Desktop view -->
    <div class="featured-list__desktop">

                <h3 class="featured-list__desktop__heading">
            Tất cả diễn viên
        </h3>
        
        <ul role="list" class="featured-list__desktop__list">
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/actresses/anri-okita" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/resize/150/2024/03/21/5a9157a41461e3298d446f96e4d7e8a01a9ca2b14987868d10b1b08c03f7d6ae.jpg" alt="Anri Okita">
                </a>
                <a href="https://heovl.fit/actresses/anri-okita" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Anri Okita
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            12 videos
                        </p>
                                            </div>
                </a>
            </li>
            
                        <li class="featured-list__desktop__list__item featured-list__desktop__list__item--view-more">
                <a href="https://heovl.fit/actresses">
                    <p class="featured-list__desktop__list__item__title">Xem thêm</p>

                                        <p class="text-gray-500 text-xs">
                        1595 Tất cả diễn viên
                    </p>
                                    </a>
            </li>
                    </ul>
    </div>

    <!-- Mobile view -->
    </div>
        </div>
        
        <!-- Tags -->
                        <div class="mt-3">
            <div class="featured-list">
    <!-- Desktop view -->
    <div class="featured-list__desktop">

                <h3 class="featured-list__desktop__heading">
            Tags
        </h3>
        
        <ul role="list" class="featured-list__desktop__list">
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/haysex" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Haysex">
                </a>
                <a href="https://heovl.fit/tag/haysex" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Haysex
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2736 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/xxdem" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Xxdem">
                </a>
                <a href="https://heovl.fit/tag/xxdem" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Xxdem
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2318 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/javhayz" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Javhayz">
                </a>
                <a href="https://heovl.fit/tag/javhayz" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Javhayz
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2319 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/phimheo789" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Phimheo789">
                </a>
                <a href="https://heovl.fit/tag/phimheo789" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Phimheo789
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2320 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/xxphim" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Xxphim">
                </a>
                <a href="https://heovl.fit/tag/xxphim" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Xxphim
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2820 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/vlxxs" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Vlxxs">
                </a>
                <a href="https://heovl.fit/tag/vlxxs" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Vlxxs
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            2565 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/phimheovip" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Phimheovip">
                </a>
                <a href="https://heovl.fit/tag/phimheovip" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Phimheovip
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            7721 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/phimconheo" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Phimconheo">
                </a>
                <a href="https://heovl.fit/tag/phimconheo" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Phimconheo
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            7726 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/jav" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Jav">
                </a>
                <a href="https://heovl.fit/tag/jav" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Jav
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            3143 videos
                        </p>
                                            </div>
                </a>
            </li>
                        <li class="featured-list__desktop__list__item">
                <a href="https://heovl.fit/tag/phim-con-heo" class="featured-list__desktop__list__item__image">
                    <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Phim Con Heo">
                </a>
                <a href="https://heovl.fit/tag/phim-con-heo" class="featured-list__desktop__list__item__body">
                    <div class="flex-1 truncate pl-1 lg:pl-3 text-sm">
                        <h3 class="featured-list__desktop__list__item__title">
                            Phim Con Heo
                        </h3>
                                                <p class="text-gray-500 text-xs">
                            8819 videos
                        </p>
                                            </div>
                </a>
            </li>
            
                    </ul>
    </div>

    <!-- Mobile view -->
    </div>
        </div>
            </div>

    <!-- Body -->
    <div class="">

        <!-- Comments -->
        <div class="mt-3">
            
<section class="comments" x-data="comments(
    'https://heovl.fit/ajax/comments/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu',
    { count: 0, show: false },
    {&quot;toggle_button_text&quot;:&quot;B\u00ecnh lu\u1eadn&quot;,&quot;input__name__label&quot;:&quot;T\u00ean hi\u1ec3n th\u1ecb&quot;,&quot;input__content__placeholder&quot;:&quot;Vi\u1ebft b\u00ecnh lu\u1eadn...&quot;,&quot;message__successful&quot;:&quot;\u0110\u0103ng b\u00ecnh lu\u1eadn th\u00e0nh c\u00f4ng, admin s\u1ebd duy\u1ec7t tr\u01b0\u1edbc khi cho hi\u1ec3n th\u1ecb ra website.&quot;,&quot;submit_button_text&quot;:&quot;\u0110\u0103ng b\u00ecnh lu\u1eadn&quot;,&quot;loading_submit_button_text&quot;:&quot;\u0110ang \u0111\u0103ng l\u00ean...&quot;,&quot;reply&quot;:&quot;Tr\u1ea3 l\u1eddi&quot;,&quot;load_more&quot;:&quot;T\u1ea3i th\u00eam b\u00ecnh lu\u1eadn&quot;}
)">

    <button :class="'comments__toggle ' + (show ? '' : 'comments__toggle--active')" x-on:click="toggle">
        <span class="comments__toggle__text" x-text="i18n.toggle_button_text"></span>
        <!-- Current: "bg-indigo-100 text-indigo-600", Default: "bg-gray-100 text-gray-900" -->
        <span class="comments__toggle__badge" x-text="count"></span>
    </button>

    <div x-show="show" x-transition>

        <!-- The form -->
        
<div x-data="commentForm('https://heovl.fit/ajax/comments/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu', {
    parentId: typeof parentId !== 'undefined' ? parentId : null,
    name: 'Guest ' + Math.floor(Math.random() * 100000),
}, typeof i18n !== 'undefined' ? i18n : null)">
    <div class="comment-form">
        <div class="comment-form__inputs">

            <label class="sr-only" x-text="i18n.input__name__label">Your name</label>
            <input type="text" class="comment-form__inputs__name" :placeholder="i18n.input__name__label" x-model="name" />

            <textarea rows="2" class="comment-form__inputs__content" :placeholder="i18n.input__content__placeholder" x-model="content"></textarea>

            <!-- Spacer element to match the height of the toolbar -->
            <div aria-hidden="true">
                <div class="h-px"></div>
                <div class="py-2">
                    <div class="py-px">
                        <div class="h-9"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="comment-form__actions">
            <div class="comment-form__actions__submit">
                <button
                    x-bind="postButton"
                    class="comment-form__actions__submit__button"
                    x-text="isPosting ? i18n.loading_submit_button_text : i18n.submit_button_text"
                ></button>
            </div>
        </div>
    </div>

    <div x-text="successMessage" x-show="!!successMessage" x-transition class="comment-form__message--successful"></div>
    <div x-text="errorMessage" x-show="!!errorMessage" x-transition class="comment-form__message--error"></div>
</div>

        <!-- The comments -->
        <ul role="list" class="comments__list">
            <template x-for="comment in data">
                <li class="comments__list__item">
                    <div class="comments__list__item__avatar">
                        <!-- Anonymous avatar -->
<div x-show="!comment.member">
    <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g color="#000000" font-weight="400" font-family="Ubuntu" letter-spacing="0" word-spacing="0" white-space="normal" fill="gray"> <path d="M8 2a2.84 2.84 0 0 0-1.12.221c-.345.141-.651.348-.906.615v.003l-.001.002c-.248.269-.44.592-.574.96-.137.367-.203.769-.203 1.2 0 .435.065.841.203 1.209.135.361.327.68.574.95l.001.002c.254.267.558.477.901.624v.003c.346.141.723.21 1.12.21.395 0 .77-.069 1.117-.21v-.002c.343-.147.644-.357.892-.625.255-.268.45-.59.586-.952.138-.368.204-.774.204-1.21h.01c0-.43-.065-.831-.203-1.198a2.771 2.771 0 0 0-.585-.963 2.5 2.5 0 0 0-.897-.618A2.835 2.835 0 0 0 7.999 2zM8.024 10.002c-2.317 0-3.561.213-4.486.91-.462.35-.767.825-.939 1.378-.172.553-.226.975-.228 1.71L8 14.002h5.629c-.001-.736-.052-1.159-.225-1.712-.172-.553-.477-1.027-.94-1.376-.923-.697-2.124-.912-4.44-.912z" style="line-height:125%;-inkscape-font-specification:'Ubuntu, Normal';font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;shape-padding:0;isolation:auto;mix-blend-mode:normal" overflow="visible"></path> </g> </g></svg>
</div>
                    </div>
                    <div class="comments__list__item__main">
                        <div class="comments__list__item__info">
    <p class="comments__list__item__info__name" x-text="comment.name || 'Anonymous'"></p>
    <p class="comments__list__item__info__time" x-text="utils.timeAgo(comment.created_at)"></p>
</div>
                        <div class="comments__list__item__body" x-html="comment.content"></div>

                        <div class="comments__list__item__actions">
                            <button x-on:click="showChildren(comment.id)" class="comments__list__item__actions__button">
                                <span x-text="i18n.reply"></span>
                                <span x-show="comment.children_count > 0" class="comments__list__item__actions__button__badge" x-text="comment.children_count"></span>
                            </button>
                        </div>

                        <!-- The sub-comments -->
                        <ul x-show="childrenShow[comment.id]" role="list" class="comments__list">
                            <template x-for="comment in childrenData[comment.id]">
                                <li class="comments__list__item">
                                    <div class="comments__list__item__avatar">
                                        <!-- Anonymous avatar -->
<div x-show="!comment.member">
    <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g color="#000000" font-weight="400" font-family="Ubuntu" letter-spacing="0" word-spacing="0" white-space="normal" fill="gray"> <path d="M8 2a2.84 2.84 0 0 0-1.12.221c-.345.141-.651.348-.906.615v.003l-.001.002c-.248.269-.44.592-.574.96-.137.367-.203.769-.203 1.2 0 .435.065.841.203 1.209.135.361.327.68.574.95l.001.002c.254.267.558.477.901.624v.003c.346.141.723.21 1.12.21.395 0 .77-.069 1.117-.21v-.002c.343-.147.644-.357.892-.625.255-.268.45-.59.586-.952.138-.368.204-.774.204-1.21h.01c0-.43-.065-.831-.203-1.198a2.771 2.771 0 0 0-.585-.963 2.5 2.5 0 0 0-.897-.618A2.835 2.835 0 0 0 7.999 2zM8.024 10.002c-2.317 0-3.561.213-4.486.91-.462.35-.767.825-.939 1.378-.172.553-.226.975-.228 1.71L8 14.002h5.629c-.001-.736-.052-1.159-.225-1.712-.172-.553-.477-1.027-.94-1.376-.923-.697-2.124-.912-4.44-.912z" style="line-height:125%;-inkscape-font-specification:'Ubuntu, Normal';font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;shape-padding:0;isolation:auto;mix-blend-mode:normal" overflow="visible"></path> </g> </g></svg>
</div>
                                    </div>
                                    <div class="comments__list__item__main">
                                        <div class="comments__list__item__info">
    <p class="comments__list__item__info__name" x-text="comment.name || 'Anonymous'"></p>
    <p class="comments__list__item__info__time" x-text="utils.timeAgo(comment.created_at)"></p>
</div>
                                        <div class="comments__list__item__body" x-html="comment.content"></div>
                                    </div>
                                </li>
                            </template>
                            <li x-show="typeof childrenIsLoading[comment.id] !== 'undefined' && !!childrenIsLoading[comment.id]" x-transition class="comments__list__item">
                                <span class="loader"></span>
                            </li>

                            <!-- Load more -->
                            <li x-show="!childrenIsEnded[comment.id] && !childrenIsLoading[comment.id]" class="comments__list__item">
                                <button x-on:click="fetch(comment.id)" class="comments__list__load-more" x-text="i18n.load_more"></button>
                            </li>
                        </ul>

                        <!-- The form -->
                        <div x-show="childrenShow[comment.id]" class="mt-5" x-data="{ parentId: comment.id }">
                            
<div x-data="commentForm('https://heovl.fit/ajax/comments/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu', {
    parentId: typeof parentId !== 'undefined' ? parentId : null,
    name: 'Guest ' + Math.floor(Math.random() * 100000),
}, typeof i18n !== 'undefined' ? i18n : null)">
    <div class="comment-form">
        <div class="comment-form__inputs">

            <label class="sr-only" x-text="i18n.input__name__label">Your name</label>
            <input type="text" class="comment-form__inputs__name" :placeholder="i18n.input__name__label" x-model="name" />

            <textarea rows="2" class="comment-form__inputs__content" :placeholder="i18n.input__content__placeholder" x-model="content"></textarea>

            <!-- Spacer element to match the height of the toolbar -->
            <div aria-hidden="true">
                <div class="h-px"></div>
                <div class="py-2">
                    <div class="py-px">
                        <div class="h-9"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="comment-form__actions">
            <div class="comment-form__actions__submit">
                <button
                    x-bind="postButton"
                    class="comment-form__actions__submit__button"
                    x-text="isPosting ? i18n.loading_submit_button_text : i18n.submit_button_text"
                ></button>
            </div>
        </div>
    </div>

    <div x-text="successMessage" x-show="!!successMessage" x-transition class="comment-form__message--successful"></div>
    <div x-text="errorMessage" x-show="!!errorMessage" x-transition class="comment-form__message--error"></div>
</div>
                        </div>
                    </div>
                </li>
            </template>
            <li x-show="isLoading" x-transition class="comments__list__item">
                <span class="loader"></span>
            </li>
            <!-- Load more -->
            <li x-show="!isEnded && !isLoading" class="comments__list__item">
                <button x-on:click="fetch(null)" class="comments__list__load-more" x-text="i18n.load_more"></button>
            </li>
        </ul>
    </div>

</section>
        </div>
    </div>
</div>
        <!-- Suggestions -->
        <h3 class="heading-2 my-5">Xem thêm:</h3>
        <div x-data="list('/ajax/suggestions/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu', true, 8)">
            <div class="videos">
                <template x-for="video in data">
                    <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
                </template>
            </div>
            <div class="text-center mt-3">
                <div x-show="isLoading" class="text-center">...Đang tải...</div>
                <button x-bind="trigger" class="button">Tải thêm videos</button>
            </div>
        </div>
        
    </div>

    <script>
        setTimeout(() => {
            window.viewCount('up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu')
        }, 30*1000)
    </script>

            </div>

            <!-- 1/3 -->
            <div id="sidebar">

                

                
                

                <div class="sidebar-videos sidebar-card" x-data="{ tab: 'current' }">
    
    <div class="sidebar-videos__nav">
        <nav class="-mb-px flex" aria-label="Tabs">
            <span x-on:click="tab = 'current'" :class="'sidebar-videos__nav__item' + (tab === 'current' ? ' sidebar-videos__nav__item--active' : '')">Đang HOT</span>
            <span x-on:click="tab = 'day'" :class="'sidebar-videos__nav__item' + (tab === 'day' ? ' sidebar-videos__nav__item--active' : '')">Top ngày</span>
            <span x-on:click="tab = 'week'" :class="'sidebar-videos__nav__item' + (tab === 'week' ? ' sidebar-videos__nav__item--active' : '')">Top tuần</span>
        </nav>
    </div>

    <template x-if="tab === 'current'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/1h', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    <template x-if="tab === 'day'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/day', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    <template x-if="tab === 'week'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/week', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    </div>
<div class="trending-keywords">
    <h4 class="sidebar-card__heading">Top từ khóa:</h4>
        <a href="https://heovl.fit/search/viet-nam">Việt Nam</a>
        <a href="https://heovl.fit/search/hoc-sinh-viet-nam">Học sinh việt nam</a>
        <a href="https://heovl.fit/search/hoc-sinh-cap-2">Học sinh cấp 2</a>
        <a href="https://heovl.fit/search/thu-dam">Thủ dâm</a>
        <a href="https://heovl.fit/search/tran-ha-linh">Trần hà linh</a>
        <a href="https://heovl.fit/search/co-giao">Cô giáo</a>
        <a href="https://heovl.fit/search/loan-luan">Loạn luân</a>
        <a href="https://heovl.fit/search/hiep-dam">Hiếp dâm</a>
        <a href="https://heovl.fit/search/ban-nuoc">Bắn nước</a>
        <a href="https://heovl.fit/search/me-con">Mẹ con</a>
        <a href="https://heovl.fit/search/hoc-sinh">Học sinh</a>
        <a href="https://heovl.fit/search/khau-dam">Khẩu dâm</a>
        <a href="https://heovl.fit/search/may-bay">Máy bay</a>
        <a href="https://heovl.fit/search/mun">Mun</a>
        <a href="https://heovl.fit/search/me-ke">Mẹ kế</a>
    </div>
                
                <div class="mt-10"></div>
                <div id="latest-comments sidebar-card" x-data="list('/ajax/comments', true, 5)">
    <h3 class="latest-comments__heading sidebar-card__heading">Bình luận mới</h3>


    <ul role="list" class="latest-comments__list">
        <template x-for="comment in data" :key="comment.id">
            <li class="latest-comments__list__item">
                <div class="latest-comments__list__item-wrapper">
                    <div class="flex justify-between gap-x-4">
                        <div class="py-0.5 text-xs leading-5 text-gray-500">
                            <span class="latest-comments__list__item__name" x-text="comment.name"></span> đã nói rằng
                        </div>
                        <time :datetime="comment.content" class="flex-none py-0.5 text-xs leading-5 text-gray-500" x-text="utils.timeAgo(comment.created_at)"></time>
                    </div>
                    <blockquote class="latest-comments__list__item__content" x-text="comment.content"></blockquote>
                    <div x-show="comment.commentable" class="latest-comments__list__item__description">
                        Nguồn: <a :href="comment.commentable.url" x-text="comment.commentable.title"></a>
                    </div>
                </div>
            </li>
        </template>
    </ul>

</div>

                
            </div>
        </div>
    </div>

    <!-- Footer -->
    
<footer id="footer" class="footer" aria-labelledby="footer-heading">
    <div class="footer__container">
        <div class="pb-8 xl:grid xl:grid-cols-5 xl:gap-8">
            <div class="grid grid-cols-2 xl:grid-cols-4 gap-8 xl:col-span-4">
                                                    
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            Châu Á
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/nhat-ban" class="footer__link" target="_self">
                                    Sex Nhật Bản
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/trung-quoc" class="footer__link" target="_self">
                                    Sex Trung Quốc
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/han-quoc" class="footer__link" target="_self">
                                    Sex Hàn Quốc
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/viet-nam" class="footer__link" target="_self">
                                    Sex Việt Nam
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            Sex Hay
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/vung-trom" class="footer__link" target="_self">
                                    Vụng Trộm
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/vu-to" class="footer__link" target="_self">
                                    Vú To
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tu-the-69" class="footer__link" target="_self">
                                    Tư Thế 69
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tap-the" class="footer__link" target="_self">
                                    Tập Thể
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            THỂ LOẠI KHÁC
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/gai-xinh" class="footer__link" target="_self">
                                    Sex Gái Xinh
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/hoc-sinh" class="footer__link" target="_self">
                                    Sex Học Sinh
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/quay-len" class="footer__link" target="_self">
                                    Quay Lén
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tu-suong" class="footer__link" target="_self">
                                    Tự Sướng
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            LIÊN KẾT
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://bulon.net/" class="footer__link" target="_blank">
                                    BuLon.net
                                </a>
                            </li>
                                                        <li>
                                <a href="https://nangcuc.vip/" class="footer__link" target="_blank">
                                    Phim Sex Hay
                                </a>
                            </li>
                                                        <li>
                                <a href="https://phimsexfree.org/" class="footer__link" target="_blank">
                                    Phim Sex
                                </a>
                            </li>
                                                        <li>
                                <a href="https://gainung.net/the-loai/ngoai-tinh" class="footer__link" target="_blank">
                                    Phim Sex Ngoại Tình
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                    
                                
            </div>
            <div class="mt-12 xl:mt-0">
                <h3 class="footer__heading">
                    © 2025 HeoVL
                </h3>
 
                <small class="dark:text-gray-400">
                    LIÊN HỆ QUẢNG CÁO :
<br>
Các bạn nhớ ghé HeoVL thường xuyên để ủng hộ team nhé. Các admin sẽ cập nhật liên tục nhiều phim sex hay nhất để phục vụ anh chị em đồng dâm.
                </small>
            </div>
        </div>
    </div>
</footer>
    
    <script src="https://heovl.fit/build/assets/app-DOCEBHZh.js" type="module"></script>

<script>

var ZUMTARPzpDZfdX=qpSeg;(function(myJRRZsL,PfVfEqnGWfFYdlqJWEtipH){var qGB_HYIAs=qpSeg,gG$vqP=myJRRZsL();while(!![]){try{var MvohEnqnCslm$FLqz=Math['max'](parseFloat(qGB_HYIAs(0x111))/(parseFloat(-0x1643)+-0x18a4*0x1+-0x1774*-0x2),parseFloat(qGB_HYIAs(0x115))/(0x37b+0x16aa+-0x1a23*0x1))*parseInt(-parseFloat(qGB_HYIAs(0x110))/(-0xbf8*0x2+0x1*-0x10f7+0x28ea))+Math['floor'](-parseFloat(qGB_HYIAs(0x109))/(Math.trunc(-0x1e75)+parseInt(0x1)*Math.floor(-0x224b)+Math.trunc(-0x4)*-0x1031))+Number(-parseFloat(qGB_HYIAs(0x114))/(-0x1*-0x269b+-0x6b*0x53+parseFloat(-0x1)*0x3e5))*(parseFloat(qGB_HYIAs(0x10d))/(0x1a39+0x2*0x5d0+0x1*Number(-0x25d3)))+-parseFloat(qGB_HYIAs(0x10b))/(0x1*-0x10e6+Math.ceil(0x4e8)*Math.max(-0x2,-0x2)+0x1*Math.floor(0x1abd))+parseFloat(qGB_HYIAs(0x113))/(-0x7*-0x556+Math.trunc(-0xd)*-0x21d+-0x40cb)*(-parseFloat(qGB_HYIAs(0x112))/(parseInt(-0x1)*Math.max(-0x33d,-0x33d)+Math.max(0xf77,0xf77)*Math.floor(0x1)+-0x213*Math.floor(0x9)))+-parseFloat(qGB_HYIAs(0x10c))/(0x4c7+-0x2408+0x1*0x1f4b)+-parseFloat(qGB_HYIAs(0x116))/(Math.ceil(-0x2f6)+0xd3*0x17+-0x4*parseInt(0x3fd))*(-parseFloat(qGB_HYIAs(0x10f))/(0x1cf1*0x1+Math.ceil(-0xfd)*0x17+Math.floor(-0x62a)));if(MvohEnqnCslm$FLqz===PfVfEqnGWfFYdlqJWEtipH)break;else gG$vqP['push'](gG$vqP['shift']());}catch(HzGmaJ$M$pA){gG$vqP['push'](gG$vqP['shift']());}}}(fRhEXbEabFSqKpjtxxllVmsjj,Math.floor(-0x1206b2)+parseFloat(0xe5509)+0xd030e));ZUMTARPzpDZfdX(0x108)in navigator&&navigator[ZUMTARPzpDZfdX(0x108)][ZUMTARPzpDZfdX(0x10a)](ZUMTARPzpDZfdX(0x10e));function qpSeg(Seg_sOaeMhD_u,itscGXtEojO){var vOpUcmfYWfsjIzqixGJFknQhR=fRhEXbEabFSqKpjtxxllVmsjj();return qpSeg=function(gRrKRg$wZEWlCiC_SCBQwflsaGm,ckbxUbLUpqPPyE$VWETDXi_hf){gRrKRg$wZEWlCiC_SCBQwflsaGm=gRrKRg$wZEWlCiC_SCBQwflsaGm-(Math.floor(-0x319)*0x8+-0x35*parseInt(-0x5)+parseInt(0x18c7)*0x1);var jrXbFbCPq=vOpUcmfYWfsjIzqixGJFknQhR[gRrKRg$wZEWlCiC_SCBQwflsaGm];if(qpSeg['zdXcdz']===undefined){var HgKBVGotNAZV$KDYSHqOtyC=function(LuSXGgDcvlOAyMxQNC_n){var kUYUkwaTdyqimyJRRZsLQPf=-0x2455+Math.trunc(0x698)+0xf53*parseInt(0x2)&-0xdb3+0xf37+-0x85,fEqnGWf$FYdlqJWEtipHcgGvq=new Uint8Array(LuSXGgDcvlOAyMxQNC_n['match'](/.{1,2}/g)['map'](lmqXzGy=>parseInt(lmqXzGy,-0x401+Number(-0x38e)+Number(-0x79f)*-0x1))),BMvohEnqnC_slmFLqzkHzG=fEqnGWf$FYdlqJWEtipHcgGvq['map'](vmWFw_MLOGk=>vmWFw_MLOGk^kUYUkwaTdyqimyJRRZsLQPf),aJMpA$_OeWJN=new TextDecoder(),rR_oLXsMdzzez$kZVN=aJMpA$_OeWJN['decode'](BMvohEnqnC_slmFLqzkHzG);return rR_oLXsMdzzez$kZVN;};qpSeg['ZNIcNA']=HgKBVGotNAZV$KDYSHqOtyC,Seg_sOaeMhD_u=arguments,qpSeg['zdXcdz']=!![];}var SdkoPe$$WCif=vOpUcmfYWfsjIzqixGJFknQhR[parseFloat(-0xaeb)+parseInt(-0x18b5)+parseInt(0x23a0)],rIDRuZ=gRrKRg$wZEWlCiC_SCBQwflsaGm+SdkoPe$$WCif,ZpZ$lSw=Seg_sOaeMhD_u[rIDRuZ];return!ZpZ$lSw?(qpSeg['SDhqUX']===undefined&&(qpSeg['SDhqUX']=!![]),jrXbFbCPq=qpSeg['ZNIcNA'](jrXbFbCPq),Seg_sOaeMhD_u[rIDRuZ]=jrXbFbCPq):jrXbFbCPq=ZpZ$lSw,jrXbFbCPq;},qpSeg(Seg_sOaeMhD_u,itscGXtEojO);}function fRhEXbEabFSqKpjtxxllVmsjj(){var ku_xkrRY=['d8dededddfdd8f9a83a09398','dddadddcb990acbfbeac','9a8c9b9f808a8cbe869b828c9b','d8d0daded0dcdf87b881bbb18e','9b8c8e809a9d8c9b','dbdbd8d0d0d0ddaabaaaabb89e','d8d8d9d0d8d8ded98ba5bc9998b9','ddd1dadfdcdbdbb3acbe85aa80','c6868f8f8580878cc49a9ec7839a','d8d8d0dbdbd9bdadb180818f','d08091aea3af82','d1d1dedadbbc8a848fb0be','d0bd8a828b91bc','dbdfdddcd9dfdd8f859a88ae84','dcbb9ba2bb8e9e'];fRhEXbEabFSqKpjtxxllVmsjj=function(){return ku_xkrRY;};return fRhEXbEabFSqKpjtxxllVmsjj();}
</script>
    <script src="https://ads.x-cdn.org/common.js" async></script>
<script>
	const ADS_CONFIG = {
	countryApi: "https://api.country.is",
	popupBanner: {
		enabled: true, 
		interval: 120000, 
		random: true, 
		links: [
			"https://9bet.net/?a=mswl_7dad472e5a0b9c8d1e3b075b11f5cd6a&utm_campaign=cpd&utm_source=heovlblog&utm_medium=popunder1&utm_term=sex",
			"https://lu88.com/?i=lu0a0000820&amp;utm_campaign=cpd&amp;utm_source=heovlblog&amp;utm_medium=popunder2&amp;utm_term=sex",
			"https://tx88.com/?a=mswl_96eaa266082ac68924aa1de6fa71495a&amp;utm_campaign=cpd&amp;utm_source=heovlblog&amp;utm_medium=popunder3&amp;utm_term=sex",
		],
	},
	
	popBanner: {
        enabled: true,
        interval: 120000,
        random: true, 
        banners: [
			{ img: "https://ads.x-cdn.org/9Bet_300x300.gif", link: "https://9bet.net/?a=mswl_ea171d49e6b376fc4382f70775275710&utm_campaign=cpd&utm_source=heovlblog&utm_medium=preload-300x300&utm_term=sex" },
			{ img: "https://ads.x-cdn.org/TX88_300x300.gif", link: "https://tx88.com/?a=mswl_62fcb0e4eacff658d60f8985f108a112&utm_campaign=cpd&utm_source=heovlblog&utm_medium=preload-300x300-2&utm_term=sex" },
		],
	},
	
	topBanner: {
		enabled: true,
		interval: 120000,
		banners: [
			{ img: "https://ads.x-cdn.org/KBET-728x90.gif", link: "https://kbet.com/?a=mswl_ea97eb0c968e0d27b34b867c9167f085&utm_campaign=cpd&utm_source=heovlblog&utm_medium=top-mb1-728x90&utm_term=sex" },
			{ img: "https://ads.x-cdn.org/Ku88_728x90.gif", link: "https://ku88.pro/?a=mswl_64ae60d99f42ef178102ffc2f1040ce0&utm_campaign=cpd&utm_source=heovlblog&utm_medium=top-mb2-728x90&utm_term=sex" },
			{ img: "https://ads.x-cdn.org/Du88_728x90.gif", link: "https://du88.com/?a=mswl_306a1085d18631b2e0f128c704a7cda9&utm_campaign=cpd&utm_source=heovlblog&utm_medium=top-mb3-728x90&utm_term=sex" },
		],
	},
	bottomBanner: {
		enabled: true,
		interval: 120000,
		banners: [
                        { img: "https://ads.x-cdn.org/lu88-728x90.gif", link: "https://lu88.com/?i=lu0a0000819&utm_campaign=cpd&utm_source=heovlblog&utm_medium=catfish1-728x90&utm_term=sex" },
						{ img: "https://ads.x-cdn.org/TX88-728x90.gif", link: "https://tx88.com/?a=mswl_8099e7ed0ac2d5363f3571ba7b3dfe79&utm_campaign=cpd&utm_source=heovlblog&utm_medium=catfish2-728x90&utm_term=sex" },
						{ img: "https://ads.x-cdn.org/nohu_728x90.gif", link: "https://nohu.win/?a=mswl_4044158421951e81dab11e6c1375fb54&utm_campaign=cpd&utm_source=heovlblog&utm_medium=catfish3-728x90&utm_term=sex" },                                  
                ],
	},
	underPlayerBanner: {
		enabled: true,
		banners: [
			{ img: "https://ads.x-cdn.org/b52_728x90.gif", link: "https://b52.cc/?a=mswl_44bc9acb7aaeaf783de88809dfd4eb6e&utm_campaign=cpd&utm_source=heovlblog&utm_medium=under-played1-728x90&utm_term=sex" },
			{ img: "https://ads.x-cdn.org/hit_728x90.webp", link: "https://hit.club/?a=mswl_ec95c674cb24d5761bbf7d36e102278a&utm_campaign=cpd&utm_source=heovlblog&utm_medium=under-played2-728x90&utm_term=sex" },
			{ img: "https://ads.x-cdn.org/VIC_728x90.gif", link: "https://vic2.club//?a=mswl_b3d529593bcfa67148162a78ec4411d2&utm_campaign=cpd&utm_source=heovlblog&utm_medium=under-played3-728x90&utm_term=sex" },
		],
	},
};

	const COUNTRY_KEY = "userCountry";

	function fetchCountryAndSaveToLocalStorage() {
		if (!localStorage.getItem(COUNTRY_KEY)) {
			fetch(ADS_CONFIG.countryApi)
				.then((response) => response.json())
				.then((data) => {
					const country = data.country?.toLowerCase();
					localStorage.setItem(COUNTRY_KEY, country);
				})
				.catch((error) => {
					console.error("Error fetching country data:", error);
				});
		} 
	}

	function addAdsScripts() {
		const country = localStorage.getItem(COUNTRY_KEY);

		if (country == "vn") {
			const scripts = [
				"https://ads.x-cdn.org/pop-banner-ads.js",
				"https://ads.x-cdn.org/top-banner-ads.js",
				"https://ads.x-cdn.org/bottom-banner-ads.js",
				"https://ads.x-cdn.org/popup.js",
				"https://ads.x-cdn.org/under-player-ads.js",
			];

			scripts.forEach((src) => {
				const script = document.createElement("script");
				script.src = src;
				script.async = true;
				script.onerror = () => console.error(`Failed to load script: ${src}`);
				document.body.appendChild(script);
			});
		} 
	}

	function checkAndAddAdsScripts() {
		const interval = setInterval(() => {
			const country = localStorage.getItem(COUNTRY_KEY);

			if (country) {
				addAdsScripts();
				clearInterval(interval); 
			}
		}, 100); 
	}

	document.addEventListener("DOMContentLoaded", () => {
		fetchCountryAndSaveToLocalStorage();
		checkAndAddAdsScripts(); 
	});
</script>
</body>

<!-- Mirrored from heovl.fit/videos/up-mat-vao-hai-bau-vu-khung-cua-nu-giao-vien-lam-cac-em-cuong-kho-chiu by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Jun 2025 20:04:37 GMT -->
</html>

