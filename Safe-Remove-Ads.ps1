# Script an toan chi xoa ads co ban
param([string]$Path = "heovl.fit")

Write-Host "=== SCRIPT AN TOAN XOA ADS ===" -ForegroundColor Green

$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html"
$processedCount = 0

foreach ($file in $htmlFiles) {
    try {
        # Tao backup truoc
        $backupPath = $file.FullName + ".backup_safe"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $file.FullName $backupPath
        }
        
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Chi xoa nhung thu an toan
        $content = $content -replace '(?s)<!-- Google tag.*?</script>', ''
        $content = $content -replace '<meta name="google-site-verification"[^>]*>', ''
        $content = $content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
        
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        $processedCount++
    }
    catch {
        Write-Host "Loi: $($file.Name)" -ForegroundColor Red
    }
}

Write-Host "Da xu ly $processedCount file an toan" -ForegroundColor Green
