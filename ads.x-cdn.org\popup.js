(function () {
    const CONFIG = ADS_CONFIG.popupBanner;
    const POPUP_KEY = "lastPopupCloseTime";
  
    if (!CONFIG.enabled) return;
  
    let adIndex = 0;
  
    function openAdLink() {
      if (!CONFIG.links || CONFIG.links.length === 0) return;
  
      let linkToOpen;
  
      if (CONFIG.random) {
        const randomIndex = Math.floor(Math.random() * CONFIG.links.length);
        linkToOpen = CONFIG.links[randomIndex];
      } else {
        linkToOpen = CONFIG.links[adIndex];
        adIndex = (adIndex + 1) % CONFIG.links.length; 
      }
  
      window.open(linkToOpen, "_blank");
      console.log(`Popup opened with link: ${linkToOpen}`);
    }
  
    function handleClick(event) {
      if (event.target.closest(".overlay-ad")) {
        console.log("Click ignored on overlay-ad element.");
        return;
      }
  
      const lastCloseTime = getCloseTimeFromLocalStorage(POPUP_KEY);
      const currentTime = Date.now();
  
      if (currentTime - lastCloseTime >= CONFIG.interval) {
        openAdLink();
        saveCloseTimeToLocalStorage(POPUP_KEY);
      }
    }
  
    function attachClickHandler() {
      document.addEventListener("click", handleClick);
    }
  
    attachClickHandler();
  })();