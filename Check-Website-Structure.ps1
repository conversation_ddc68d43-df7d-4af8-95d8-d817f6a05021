# Script kiểm tra cấu trúc website và sửa đường dẫn
# T<PERSON><PERSON><PERSON>: AI Assistant

param(
    [string]$Path = "heovl.fit"
)

Write-Host "=== KIỂM TRA CẤU TRÚC WEBSITE ===" -ForegroundColor Green
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Không tìm thấy thư mục: $Path" -ForegroundColor Red
    exit 1
}

# Kiểm tra các thư mục quan trọng
$requiredFolders = @(
    "assets",
    "build", 
    "resize",
    "videos"
)

Write-Host "Kiểm tra thư mục..." -ForegroundColor Yellow
foreach ($folder in $requiredFolders) {
    $folderPath = Join-Path $Path $folder
    if (Test-Path $folderPath) {
        Write-Host "✓ $folder" -ForegroundColor Green
    } else {
        Write-Host "✗ $folder (thiếu)" -ForegroundColor Red
        
        # Tạo thư mục thiếu
        try {
            New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
            Write-Host "  → Đã tạo thư mục $folder" -ForegroundColor Yellow
        } catch {
            Write-Host "  → Không thể tạo thư mục $folder" -ForegroundColor Red
        }
    }
}

# Kiểm tra file CSS/JS quan trọng
Write-Host ""
Write-Host "Kiểm tra file CSS/JS..." -ForegroundColor Yellow

$cssPath = Join-Path $Path "build\assets\app-Cb5tGUTM.css"
if (-not (Test-Path $cssPath)) {
    Write-Host "✗ CSS file thiếu: $cssPath" -ForegroundColor Red
    
    # Tạo CSS file cơ bản
    $basicCSS = @"
/* Basic CSS for offline viewing */
body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
.container { max-width: 1200px; margin: 0 auto; }
.video-player { width: 100%; height: 400px; background: #000; margin: 20px 0; }
.navbar__link { color: #fff; text-decoration: none; margin: 0 10px; }
.heading-1 { font-size: 24px; margin: 20px 0; }
.breadcrumb { list-style: none; padding: 0; }
.breadcrumb__item { display: inline; margin-right: 10px; }
.featured-list__desktop__list { list-style: none; padding: 0; }
.featured-list__desktop__list__item { margin: 10px 0; }
"@
    
    try {
        $buildDir = Join-Path $Path "build\assets"
        if (-not (Test-Path $buildDir)) {
            New-Item -Path $buildDir -ItemType Directory -Force | Out-Null
        }
        Set-Content -Path $cssPath -Value $basicCSS -Encoding UTF8
        Write-Host "  → Đã tạo CSS file cơ bản" -ForegroundColor Yellow
    } catch {
        Write-Host "  → Không thể tạo CSS file" -ForegroundColor Red
    }
} else {
    Write-Host "✓ CSS file tồn tại" -ForegroundColor Green
}

# Kiểm tra file theme
$themePath = Join-Path $Path "assets\hvl\theme0345.css"
if (-not (Test-Path $themePath)) {
    Write-Host "✗ Theme CSS thiếu: $themePath" -ForegroundColor Red
    
    $themeCSS = @"
/* Theme CSS */
.dark { background: #1a1a1a; color: #fff; }
.navbar__mobile-button { background: none; border: none; color: #fff; }
.video-player--x { background: #000; border: 1px solid #333; }
.detail-page__heading { margin: 20px 0; }
.detail-page__information { margin: 20px 0; }
"@
    
    try {
        $themeDir = Join-Path $Path "assets\hvl"
        if (-not (Test-Path $themeDir)) {
            New-Item -Path $themeDir -ItemType Directory -Force | Out-Null
        }
        Set-Content -Path $themePath -Value $themeCSS -Encoding UTF8
        Write-Host "  → Đã tạo Theme CSS" -ForegroundColor Yellow
    } catch {
        Write-Host "  → Không thể tạo Theme CSS" -ForegroundColor Red
    }
} else {
    Write-Host "✓ Theme CSS tồn tại" -ForegroundColor Green
}

# Tạo file default image
$defaultImagePath = Join-Path $Path "assets\hvl\images\default.png"
if (-not (Test-Path $defaultImagePath)) {
    Write-Host "✗ Default image thiếu" -ForegroundColor Red
    
    try {
        $imageDir = Join-Path $Path "assets\hvl\images"
        if (-not (Test-Path $imageDir)) {
            New-Item -Path $imageDir -ItemType Directory -Force | Out-Null
        }
        
        # Tạo file placeholder (1x1 pixel PNG)
        $pngBytes = [Convert]::FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")
        [System.IO.File]::WriteAllBytes($defaultImagePath, $pngBytes)
        Write-Host "  → Đã tạo default image" -ForegroundColor Yellow
    } catch {
        Write-Host "  → Không thể tạo default image" -ForegroundColor Red
    }
} else {
    Write-Host "✓ Default image tồn tại" -ForegroundColor Green
}

# Kiểm tra và đếm file HTML
Write-Host ""
Write-Host "Thống kê file..." -ForegroundColor Yellow

$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html"
$videoFiles = Get-ChildItem -Path (Join-Path $Path "videos") -Filter "*.html" -ErrorAction SilentlyContinue
$categoryFiles = Get-ChildItem -Path (Join-Path $Path "categories") -Filter "*.html" -ErrorAction SilentlyContinue

Write-Host "Tổng file HTML: $($htmlFiles.Count)" -ForegroundColor Cyan
Write-Host "File video: $($videoFiles.Count)" -ForegroundColor Cyan
Write-Host "File category: $($categoryFiles.Count)" -ForegroundColor Cyan

# Kiểm tra file index.html
$indexPath = Join-Path $Path "index.html"
if (Test-Path $indexPath) {
    Write-Host "✓ index.html tồn tại" -ForegroundColor Green
} else {
    Write-Host "✗ index.html thiếu" -ForegroundColor Red
}

# Kiểm tra dung lượng
$totalSize = (Get-ChildItem -Path $Path -Recurse | Measure-Object -Property Length -Sum).Sum
$sizeInMB = [math]::Round($totalSize / 1MB, 2)
Write-Host "Tổng dung lượng: ${sizeInMB}MB" -ForegroundColor Cyan

Write-Host ""
Write-Host "=== KHUYẾN NGHỊ ===" -ForegroundColor Green

if ($htmlFiles.Count -eq 0) {
    Write-Host "⚠️  Không tìm thấy file HTML nào!" -ForegroundColor Red
} elseif ($htmlFiles.Count -lt 10) {
    Write-Host "⚠️  Quá ít file HTML, có thể clone chưa hoàn tất" -ForegroundColor Yellow
} else {
    Write-Host "✓ Số lượng file HTML hợp lý" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== CÁCH SỬA LỖI RELOAD ===" -ForegroundColor Green
Write-Host "1. Chạy script: .\Fix-Website-Issues.ps1" -ForegroundColor White
Write-Host "2. Mở file index.html bằng trình duyệt" -ForegroundColor White
Write-Host "3. Nếu vẫn lỗi, mở F12 > Console để xem chi tiết" -ForegroundColor White
Write-Host "4. Thử tắt JavaScript trong trình duyệt" -ForegroundColor White

Write-Host ""
Write-Host "Hoàn thành kiểm tra!" -ForegroundColor Green
