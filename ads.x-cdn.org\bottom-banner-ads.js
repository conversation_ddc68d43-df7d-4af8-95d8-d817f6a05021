(function () {
  const CONFIG = ADS_CONFIG.bottomBanner;
  const BOTTOM_BANNER_KEY = "lastBottomBannerCloseTime";

  if (!CONFIG.enabled) return;

  function injectCSS() {
    const css = `
      #bottomBannerContainer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        display: none; /* Mặc định ẩn */
        justify-content: center;
        z-index: 999;
      }
      .bottom-banner-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px; /* Khoảng cách giữa hai banner */
        position: relative;
      }
      .bottom-banner-item {
        display: block;
        height: 35px;
        aspect-ratio: 728/90;
        margin: 0 auto;
        object-fit: cover;
        max-width: 728px;
      }
      @media screen and (min-width: 768px) {
        .bottom-banner-item {
          height: 50px;
          max-height: 80px; /* <PERSON><PERSON><PERSON> thước giống design ban đầu */
        }
      }
      .bottom-banner-close-btn {
        position: absolute;
        top: -20px; /* Đặt nút gần sát với banner */
        right: -20px; /* Đặt nút ở góc phải */
        background-color: #f2f2f2;
        color: red;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s ease;
      }
      .bottom-banner-close-btn:hover {
        background-color: #e0e0e0;
      }
    `;
    const style = document.createElement("style");
    style.textContent = css;
    document.head.appendChild(style);
  }

  function createBottomBanner() {
    const bottomBanner = document.createElement("div");
    bottomBanner.id = "bottomBannerContainer";
    bottomBanner.className = "overlay-ad";

    const bannerWrapper = document.createElement("div");
    bannerWrapper.className = "bottom-banner-wrapper";
    bannerWrapper.classList.add("overlay-ad");

    CONFIG.banners.forEach((ad) => {
      const banner = document.createElement("img");
	  banner.setAttribute("loading", "lazy");
      banner.src = ad.img;
      banner.alt = "Bottom Banner";
      banner.className = "bottom-banner-item";
      banner.onclick = () => window.open(ad.link);
      bannerWrapper.appendChild(banner);
    });

    const closeButton = document.createElement("button");
    closeButton.innerHTML = "×";
    closeButton.className = "bottom-banner-close-btn";
    closeButton.classList.add("overlay-ad");
    closeButton.onclick = () => {
      bottomBanner.style.display = "none";
      saveCloseTimeToLocalStorage(BOTTOM_BANNER_KEY);
      console.log("Bottom banner ads closed.");
    };

    bannerWrapper.appendChild(closeButton);
    bottomBanner.appendChild(bannerWrapper);
    document.body.appendChild(bottomBanner);
  }

  injectCSS();
  createBottomBanner();
  showAdsIfIntervalPassed(BOTTOM_BANNER_KEY, CONFIG.interval, "bottomBannerContainer");
})();