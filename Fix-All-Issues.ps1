# Script tổng hợp sửa tất cả vấn đề website
# <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>: AI Assistant

param(
    [string]$Path = "heovl.fit"
)

Write-Host "=== SỬA TẤT CẢ VẤN ĐỀ WEBSITE ===" -ForegroundColor Green
Write-Host "Đang xử lý thư mục: $Path" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "❌ Không tìm thấy thư mục: $Path" -ForegroundColor Red
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

# Bước 1: Tạo backup toàn bộ
Write-Host "🔄 Bước 1: Tạo backup..." -ForegroundColor Yellow
$backupFolder = "${Path}_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
try {
    Copy-Item -Path $Path -Destination $backupFolder -Recurse -Force
    Write-Host "✅ Backup thành công: $backupFolder" -ForegroundColor Green
} catch {
    Write-Host "⚠️  <PERSON>hông thể tạo backup: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Bước 2: <PERSON><PERSON><PERSON> thư mục ads
Write-Host ""
Write-Host "🔄 Bước 2: Xóa thư mục quảng cáo..." -ForegroundColor Yellow
$adsFolder = Join-Path $Path "ads.x-cdn.org"
if (Test-Path $adsFolder) {
    Remove-Item $adsFolder -Recurse -Force
    Write-Host "✅ Đã xóa thư mục ads" -ForegroundColor Green
} else {
    Write-Host "ℹ️  Thư mục ads không tồn tại" -ForegroundColor Cyan
}

# Bước 3: Tạo cấu trúc thư mục cần thiết
Write-Host ""
Write-Host "🔄 Bước 3: Tạo cấu trúc thư mục..." -ForegroundColor Yellow

$requiredFolders = @("assets\hvl\images", "build\assets")
foreach ($folder in $requiredFolders) {
    $folderPath = Join-Path $Path $folder
    if (-not (Test-Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
        Write-Host "✅ Tạo thư mục: $folder" -ForegroundColor Green
    }
}

# Tạo CSS cơ bản
$cssContent = @"
body{font-family:Arial,sans-serif;margin:0;padding:0;background:#1a1a1a;color:#fff}
.container{max-width:1200px;margin:0 auto;padding:20px}
.video-player{width:100%;height:400px;background:#000;border:1px solid #333;margin:20px 0}
.navbar__link{color:#fff;text-decoration:none;margin:0 10px;padding:5px 10px;border-radius:3px}
.navbar__link:hover{background:#333}
.heading-1{font-size:24px;margin:20px 0;color:#fff}
.breadcrumb{list-style:none;padding:0;margin:10px 0}
.breadcrumb__item{display:inline;margin-right:10px}
.breadcrumb__item__link{color:#ccc;text-decoration:none}
.featured-list__desktop__list{list-style:none;padding:0}
.featured-list__desktop__list__item{margin:10px 0;padding:10px;background:#333;border-radius:5px}
.dark{background:#1a1a1a;color:#fff}
"@

$cssPath = Join-Path $Path "build\assets\app-Cb5tGUTM.css"
Set-Content -Path $cssPath -Value $cssContent -Encoding UTF8

$themePath = Join-Path $Path "assets\hvl\theme0345.css"
Set-Content -Path $themePath -Value $cssContent -Encoding UTF8

Write-Host "✅ Tạo file CSS cơ bản" -ForegroundColor Green

# Bước 4: Sửa tất cả file HTML
Write-Host ""
Write-Host "🔄 Bước 4: Sửa file HTML..." -ForegroundColor Yellow

$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*backup*" }
$processedCount = 0

foreach ($file in $htmlFiles) {
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Xóa tất cả scripts external
        $content = $content -replace '(?s)<script[^>]*src="https://[^"]*"[^>]*>.*?</script>', ''
        
        # Xóa AlpineJS attributes
        $content = $content -replace 'x-data="[^"]*"', ''
        $content = $content -replace 'x-show="[^"]*"', 'style="display:block"'
        $content = $content -replace 'x-transition[^=]*="[^"]*"', ''
        $content = $content -replace 'x-on:click="[^"]*"', 'onclick="return false;"'
        
        # Xóa Google Analytics
        $content = $content -replace '(?s)<!-- Google tag.*?</script>', ''
        $content = $content -replace '(?s)<script>.*?gtag.*?</script>', ''
        
        # Xóa meta verification
        $content = $content -replace '<meta name="google-site-verification"[^>]*>', ''
        $content = $content -replace '<meta name="tlsdk"[^>]*>', ''
        
        # Sửa đường dẫn CSS/JS
        $content = $content -replace 'href="../build/', 'href="build/'
        $content = $content -replace 'href="../assets/', 'href="assets/'
        $content = $content -replace 'src="../build/', 'src="build/'
        $content = $content -replace 'src="../assets/', 'src="assets/'
        
        # Xóa ads containers
        $content = $content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
        $content = $content -replace '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
        $content = $content -replace '(?s)<style>.*?\.responsive-container.*?</style>', ''
        
        # Xóa ADS_CONFIG
        $content = $content -replace '(?s)const ADS_CONFIG = \{.*?\};', ''
        $content = $content -replace '(?s)const COUNTRY_KEY.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
        
        # Sửa video player
        $content = $content -replace '(?s)<iframe src="https://[^"]*streamqq[^"]*"[^>]*></iframe>', '<div style="background:#000;color:#fff;padding:50px;text-align:center;border:1px solid #333;">📺 Video Player<br><small>Offline Mode</small></div>'
        
        # Thêm script ngăn reload
        $preventScript = @"
<script>
window.addEventListener('load', function() {
    for (let i = 1; i < 99999; i++) {
        window.clearTimeout(i);
        window.clearInterval(i);
    }
});
window.location.replace = function() { console.log('Blocked redirect'); };
window.location.assign = function() { console.log('Blocked redirect'); };
</script>
"@
        
        if ($content -notmatch 'Blocked redirect') {
            $content = $content -replace '</head>', "$preventScript`n</head>"
        }
        
        # Dọn dẹp
        $content = $content -replace '\n{3,}', "`n`n"
        
        # Ghi lại file
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        $processedCount++
        
        if ($processedCount % 50 -eq 0) {
            Write-Host "  Đã xử lý: $processedCount file..." -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "❌ Lỗi file: $($file.Name)" -ForegroundColor Red
    }
}

Write-Host "✅ Đã sửa $processedCount file HTML" -ForegroundColor Green

# Bước 5: Tạo file index đơn giản nếu cần
Write-Host ""
Write-Host "🔄 Bước 5: Kiểm tra file index..." -ForegroundColor Yellow

$indexPath = Join-Path $Path "index.html"
if (-not (Test-Path $indexPath)) {
    $simpleIndex = @"
<!DOCTYPE html>
<html>
<head>
    <title>Website Offline</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="build/assets/app-Cb5tGUTM.css">
</head>
<body>
    <div class="container">
        <h1>Website đã được làm sạch</h1>
        <p>Website đã được xóa quảng cáo và sửa lỗi reload.</p>
        <p>Duyệt các thư mục:</p>
        <ul>
            <li><a href="videos/">Videos</a></li>
            <li><a href="categories/">Categories</a></li>
        </ul>
    </div>
</body>
</html>
"@
    Set-Content -Path $indexPath -Value $simpleIndex -Encoding UTF8
    Write-Host "✅ Tạo file index.html mới" -ForegroundColor Green
}

# Kết quả
Write-Host ""
Write-Host "🎉 HOÀN THÀNH!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Thống kê:" -ForegroundColor Cyan
Write-Host "  • Đã sửa: $processedCount file HTML" -ForegroundColor White
Write-Host "  • Backup tại: $backupFolder" -ForegroundColor White
Write-Host "  • Đã xóa quảng cáo và sửa lỗi reload" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Cách sử dụng:" -ForegroundColor Cyan
Write-Host "  1. Mở file index.html bằng trình duyệt" -ForegroundColor White
Write-Host "  2. Hoặc mở bất kỳ file HTML nào trong thư mục videos/" -ForegroundColor White
Write-Host "  3. Website sẽ không còn reload liên tục" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Lưu ý:" -ForegroundColor Yellow
Write-Host "  • Video player sẽ hiển thị 'Offline Mode'" -ForegroundColor White
Write-Host "  • Một số chức năng có thể không hoạt động do offline" -ForegroundColor White
Write-Host "  • Để khôi phục, sử dụng thư mục backup" -ForegroundColor White

Read-Host "`nNhấn Enter để thoát"
