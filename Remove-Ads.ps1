# Script PowerShell để xóa quảng cáo khỏi các file HTML
# Tác giả: AI Assistant
# <PERSON><PERSON> tả: X<PERSON><PERSON> tất cả các đoạn code quảng cáo khỏi các file HTML trong thư mục heovl.fit

param(
    [string]$Path = "heovl.fit",
    [switch]$Backup = $true,
    [switch]$Verbose = $false
)

# Hàm ghi log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    if ($Verbose) {
        Add-Content -Path "ads-removal.log" -Value $logMessage
    }
}

# Hàm tạo backup
function Create-Backup {
    param([string]$FilePath)
    if ($Backup) {
        $backupPath = $FilePath + ".backup"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $FilePath $backupPath
            Write-Log "Backup created: $backupPath" "INFO"
        }
    }
}

# Hàm xóa quảng cáo từ nội dung HTML
function Remove-Ads {
    param([string]$Content)
    
    # 1. Xóa Google Analytics/GTM scripts
    $Content = $Content -replace '(?s)<!-- Google tag \(gtag\.js\).*?</script>', ''
    $Content = $Content -replace '(?s)<script async src="https://www\.googletagmanager\.com/gtag/js.*?</script>', ''
    
    # 2. Xóa Google site verification
    $Content = $Content -replace '<meta name="google-site-verification"[^>]*>', ''
    
    # 3. Xóa tacolo verify code
    $Content = $Content -replace '<meta name="tlsdk"[^>]*>', ''
    
    # 4. Xóa ads placement divs
    $Content = $Content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
    
    # 5. Xóa under player ads container
    $Content = $Content -replace '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
    $Content = $Content -replace '(?s)<style>.*?\.responsive-container.*?</style>', ''
    
    # 6. Xóa toàn bộ ADS_CONFIG object và related scripts
    $Content = $Content -replace '(?s)const ADS_CONFIG = \{.*?\};', ''
    $Content = $Content -replace '(?s)const COUNTRY_KEY = "userCountry";.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
    
    # 7. Xóa các script ads từ ads.x-cdn.org
    $Content = $Content -replace '(?s)<script[^>]*src="[^"]*ads\.x-cdn\.org[^"]*"[^>]*>.*?</script>', ''
    
    # 8. Xóa các reference đến ads scripts trong addAdsScripts function
    $Content = $Content -replace '(?s)function addAdsScripts\(\).*?\}', ''
    $Content = $Content -replace '(?s)function fetchCountryAndSaveToLocalStorage\(\).*?\}', ''
    $Content = $Content -replace '(?s)function checkAndAddAdsScripts\(\).*?\}', ''
    
    # 9. Xóa các dòng trống thừa
    $Content = $Content -replace '(?m)^\s*$\n', ''
    $Content = $Content -replace '\n{3,}', "`n`n"
    
    return $Content
}

# Main script
Write-Log "Bắt đầu xóa quảng cáo từ các file HTML..." "INFO"

if (-not (Test-Path $Path)) {
    Write-Log "Thư mục không tồn tại: $Path" "ERROR"
    exit 1
}

# Tìm tất cả file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*.backup" }

Write-Log "Tìm thấy $($htmlFiles.Count) file HTML" "INFO"

$processedCount = 0
$errorCount = 0

foreach ($file in $htmlFiles) {
    try {
        Write-Log "Đang xử lý: $($file.FullName)" "INFO"
        
        # Tạo backup
        Create-Backup -FilePath $file.FullName
        
        # Đọc nội dung file
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Kiểm tra xem file có chứa quảng cáo không
        $hasAds = $content -match "(ads\.x-cdn\.org|ADS_CONFIG|google-site-verification|underPlayerAdsContainer|ad-place)"
        
        if ($hasAds) {
            # Xóa quảng cáo
            $cleanContent = Remove-Ads -Content $content
            
            # Ghi lại file
            Set-Content -Path $file.FullName -Value $cleanContent -Encoding UTF8
            
            Write-Log "Đã xóa quảng cáo từ: $($file.Name)" "SUCCESS"
            $processedCount++
        } else {
            Write-Log "Không tìm thấy quảng cáo trong: $($file.Name)" "INFO"
        }
    }
    catch {
        Write-Log "Lỗi khi xử lý file $($file.FullName): $($_.Exception.Message)" "ERROR"
        $errorCount++
    }
}

Write-Log "Hoàn thành!" "INFO"
Write-Log "Đã xử lý: $processedCount file" "INFO"
Write-Log "Lỗi: $errorCount file" "INFO"

if ($Backup) {
    Write-Log "Các file backup được tạo với extension .backup" "INFO"
    Write-Log "Để khôi phục, đổi tên file .backup thành .html" "INFO"
}

# Xóa thư mục ads.x-cdn.org nếu tồn tại
$adsDir = Join-Path $Path "ads.x-cdn.org"
if (Test-Path $adsDir) {
    try {
        Remove-Item $adsDir -Recurse -Force
        Write-Log "Đã xóa thư mục quảng cáo: $adsDir" "SUCCESS"
    }
    catch {
        Write-Log "Không thể xóa thư mục quảng cáo: $($_.Exception.Message)" "ERROR"
    }
}

Write-Log "Script hoàn thành!" "INFO"
