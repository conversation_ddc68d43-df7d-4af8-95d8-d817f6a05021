# Script kiem tra HTML nang cao va xoa file hu
# Tac gia: AI Assistant

param(
    [string]$Path = "heovl.fit",
    [switch]$DryRun = $false,
    [switch]$Verbose = $false,
    [switch]$CheckContent = $true,
    [switch]$CheckSize = $true,
    [switch]$CheckStructure = $true,
    [int]$MinSize = 500,
    [switch]$Interactive = $true
)

Write-Host "=== KIEM TRA HTML NANG CAO ===" -ForegroundColor Green
Write-Host "Thu muc: $Path" -ForegroundColor Cyan
Write-Host "Kich thuoc toi thieu: $MinSize bytes" -ForegroundColor Cyan
if ($DryRun) { Write-Host "CHE DO KIEM TRA" -ForegroundColor Yellow }
Write-Host ""

# Ham kiem tra nang cao
function Test-HTMLAdvanced {
    param([string]$FilePath)
    
    $issues = @()
    
    try {
        $content = Get-Content -Path $FilePath -Raw -Encoding UTF8 -ErrorAction Stop
        $fileInfo = Get-Item $FilePath
        
        # 1. Kiem tra file rong
        if ([string]::IsNullOrWhiteSpace($content)) {
            $issues += "File rong hoac chi co khoang trang"
        }
        
        # 2. Kiem tra kich thuoc
        if ($CheckSize -and $fileInfo.Length -lt $MinSize) {
            $issues += "File qua nho ($($fileInfo.Length) bytes < $MinSize bytes)"
        }
        
        # 3. Kiem tra cau truc HTML co ban
        if ($CheckStructure) {
            if ($content -notmatch '<!DOCTYPE\s+html>') {
                $issues += "Thieu DOCTYPE html"
            }
            
            if ($content -notmatch '<html[^>]*>') {
                $issues += "Thieu the mo <html>"
            }
            
            if ($content -notmatch '</html>') {
                $issues += "Thieu the dong </html>"
            }
            
            if ($content -notmatch '<head[^>]*>') {
                $issues += "Thieu the mo <head>"
            }
            
            if ($content -notmatch '</head>') {
                $issues += "Thieu the dong </head>"
            }
            
            if ($content -notmatch '<body[^>]*>') {
                $issues += "Thieu the mo <body>"
            }
            
            if ($content -notmatch '</body>') {
                $issues += "Thieu the dong </body>"
            }
            
            if ($content -notmatch '<title[^>]*>.*?</title>') {
                $issues += "Thieu the <title>"
            }
        }
        
        # 4. Kiem tra noi dung
        if ($CheckContent) {
            # Kiem tra co noi dung trong body khong
            if ($content -match '(?s)<body[^>]*>(.*?)</body>') {
                $bodyContent = $matches[1]
                # Xoa script, style, comment
                $bodyContent = $bodyContent -replace '(?s)<script[^>]*>.*?</script>', ''
                $bodyContent = $bodyContent -replace '(?s)<style[^>]*>.*?</style>', ''
                $bodyContent = $bodyContent -replace '(?s)<!--.*?-->', ''
                # Xoa tat ca the HTML
                $bodyContent = $bodyContent -replace '<[^>]+>', ''
                # Xoa khoang trang
                $bodyContent = $bodyContent -replace '\s+', ' '
                $bodyContent = $bodyContent.Trim()
                
                if ($bodyContent.Length -lt 20) {
                    $issues += "Khong co noi dung chinh trong body"
                }
            } else {
                $issues += "Khong tim thay noi dung body"
            }
            
            # Kiem tra co loi cu phap nghiem trong
            $openTags = [regex]::Matches($content, '<(\w+)[^>]*(?<!/)>').Count
            $closeTags = [regex]::Matches($content, '</(\w+)>').Count
            $selfClosingTags = [regex]::Matches($content, '<\w+[^>]*/\s*>').Count
            
            $expectedCloseTags = $openTags - $selfClosingTags
            $tagImbalance = [Math]::Abs($expectedCloseTags - $closeTags)
            
            if ($tagImbalance -gt ($openTags * 0.3)) {
                $issues += "The HTML khong can bang nghiem trong (mo: $openTags, dong: $closeTags, tu dong: $selfClosingTags)"
            }
        }
        
        # 5. Kiem tra encoding
        if ($content -match '[^\x00-\x7F]' -and $content -notmatch 'charset=') {
            $issues += "Co ky tu Unicode nhung thieu khai bao charset"
        }
        
        # 6. Kiem tra file co bi cat ngang khong
        if ($content -notmatch '</html>\s*$') {
            $issues += "File co the bi cat ngang (khong ket thuc bang </html>)"
        }
        
        return @{
            IsValid = ($issues.Count -eq 0)
            Issues = $issues
            Size = $fileInfo.Length
            ContentLength = $content.Length
        }
    }
    catch {
        return @{
            IsValid = $false
            Issues = @("Loi doc file: $($_.Exception.Message)")
            Size = 0
            ContentLength = 0
        }
    }
}

# Tim file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*backup*" }
Write-Host "Tim thay $($htmlFiles.Count) file HTML" -ForegroundColor Cyan

if ($htmlFiles.Count -eq 0) {
    Write-Host "Khong tim thay file HTML nao" -ForegroundColor Yellow
    exit 0
}

# Kiem tra file
$validFiles = @()
$brokenFiles = @()
$totalSize = 0

Write-Host ""
Write-Host "Dang kiem tra..." -ForegroundColor Yellow

foreach ($file in $htmlFiles) {
    $result = Test-HTMLAdvanced -FilePath $file.FullName
    $totalSize += $result.Size
    
    if ($result.IsValid) {
        $validFiles += $file
        if ($Verbose) {
            Write-Host "OK  : $($file.Name) ($([math]::Round($result.Size/1KB,2)) KB)" -ForegroundColor Green
        }
    } else {
        $brokenFiles += @{
            File = $file
            Issues = $result.Issues
            Size = $result.Size
        }
        
        Write-Host "BAD : $($file.Name) ($([math]::Round($result.Size/1KB,2)) KB)" -ForegroundColor Red
        foreach ($issue in $result.Issues) {
            Write-Host "      - $issue" -ForegroundColor Yellow
        }
    }
}

# Hien thi ket qua
Write-Host ""
Write-Host "=== KET QUA ===" -ForegroundColor Green
Write-Host "Tong file: $($htmlFiles.Count)" -ForegroundColor Cyan
Write-Host "File hop le: $($validFiles.Count)" -ForegroundColor Green
Write-Host "File bi hu: $($brokenFiles.Count)" -ForegroundColor Red
Write-Host "Tong dung luong: $([math]::Round($totalSize/1MB,2)) MB" -ForegroundColor Cyan

if ($brokenFiles.Count -gt 0) {
    $brokenSize = ($brokenFiles | ForEach-Object { $_.Size } | Measure-Object -Sum).Sum
    
    Write-Host ""
    Write-Host "=== FILE BI HU ===" -ForegroundColor Red
    Write-Host "So luong: $($brokenFiles.Count) file" -ForegroundColor Yellow
    Write-Host "Dung luong: $([math]::Round($brokenSize/1KB,2)) KB" -ForegroundColor Yellow
    
    if ($Interactive -and -not $DryRun) {
        Write-Host ""
        Write-Host "Cac file se bi xoa:" -ForegroundColor Red
        foreach ($broken in $brokenFiles) {
            Write-Host "- $($broken.File.Name)" -ForegroundColor Yellow
        }
        
        Write-Host ""
        $confirm = Read-Host "XOA $($brokenFiles.Count) file bi hu? (y/n)"
        
        if ($confirm -eq 'y' -or $confirm -eq 'Y') {
            $deletedCount = 0
            $deletedSize = 0
            
            foreach ($broken in $brokenFiles) {
                try {
                    Remove-Item $broken.File.FullName -Force
                    Write-Host "Da xoa: $($broken.File.Name)" -ForegroundColor Red
                    $deletedCount++
                    $deletedSize += $broken.Size
                }
                catch {
                    Write-Host "Loi xoa: $($broken.File.Name)" -ForegroundColor Red
                }
            }
            
            Write-Host ""
            Write-Host "Da xoa: $deletedCount file ($([math]::Round($deletedSize/1KB,2)) KB)" -ForegroundColor Green
        }
    } elseif ($DryRun) {
        Write-Host ""
        Write-Host "CHE DO DRY RUN - Khong xoa file" -ForegroundColor Yellow
    }
} else {
    Write-Host ""
    Write-Host "Tat ca file HTML deu hop le!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Hoan thanh!" -ForegroundColor Green
