﻿<!DOCTYPE html>
<html class="dark">

<!-- Mirrored from heovl.fit/studio by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Jun 2025 20:04:46 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
    <title>Studio - HeoVL</title>
<meta name="description" content="" />
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

<link rel="canonical" href="https://heovl.fit/studio" />

<link rel="shortcut icon" href="https://heovl.fit/resize/50/2024/05/09/3aecc32e86cf3a79a98ed9f567354ab1fdd5d5355ddefdbb24855553b519a396.png" type="image/x-icon">

<!-- Common -->
<script>
window.setCookie = function(n, t, r) {
                    var e = new Date;
                    e.setTime(e.getTime() + 60 * r * 1e3);
                    var u = "expires=" + e.toUTCString();
                    document.cookie = n + "=" + t + ";" + u + ";path=/"
                }; 

window.getCookie = function(n) {
                    for (var t = n + "=", r = decodeURIComponent(document.cookie).split(";"), e = 0; e < r.length; e++) {
                        for (var u = r[e];
                            " " == u.charAt(0);) u = u.substring(1);
                        if (0 == u.indexOf(t)) return u.substring(t.length, u.length)
                    }
                    return ""
                }
</script>

<meta property="og:title" content="Studio - HeoVL"/>
<meta property="og:description" content=""/>
<meta property="og:url" content="https://heovl.fit/studio"/>
<meta property="og:site_name" content="HeoVL"/>
    
<meta name="twitter:card" content="summary"/>
<meta name="twitter:description" content=""/>
<meta name="twitter:title" content="Studio - HeoVL"/>
<link rel="stylesheet" href="https://heovl.fit/build/assets/app-Cb5tGUTM.css" />

<link href="https://heovl.fit/assets/hvl/theme.css?p=22" rel="stylesheet">
</head>
<body class="antialiased text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-900">

    <div class="absolute z-20 top-0 inset-x-0 flex justify-center overflow-hidden pointer-events-none" style="">
    <div class="w-[108rem] flex-none flex justify-end">
        <picture>
            <source srcset="https://heovl.fit/assets/hvl/img/background/light.avif?v=2" type="image/avif">
            <img src="https://heovl.fit/assets/hvl/img/background/light.png?v=2" alt="" class="w-[71.75rem] flex-none max-w-none dark:hidden">
        </picture>
        <picture>
            <source srcset="https://heovl.fit/assets/hvl/img/background/dark.avif?v=2" type="image/avif">
            <img src="https://heovl.fit/assets/hvl/img/background/dark.png?v=2" alt="" class="w-[90rem] flex-none max-w-none hidden dark:block">
        </picture>
    </div>
</div>

    <!-- Navbar -->
    

<nav id="navbar" x-data="{ mobileMenuShow: false }">
    <div class="max-w-screen-2xl mx-auto px-3">
        <div class="flex justify-between h-16">
            <div class="flex">
                <div class="-ml-2 mr-2 flex items-center md:hidden">
                    <!-- Mobile menu button -->
                    <button type="button" class="navbar__mobile-button" aria-controls="mobile-menu" aria-expanded="false" x-on:click="mobileMenuShow = !mobileMenuShow">
                        <span class="sr-only">Open main menu</span>
                        <!--
                          Icon when menu is closed.

                          Heroicon name: outline/menu

                          Menu open: "hidden", Menu closed: "block"
                        -->
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <!--
                          Icon when menu is open.

                          Heroicon name: outline/x

                          Menu open: "block", Menu closed: "hidden"
                        -->
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="flex-shrink-0 flex items-center">
                    <a href="https://heovl.fit/" class="text-2xl font-bold dark:text-gray-300">
                        HeoVL
                    </a>
                </div>
                <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
                    <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->

                                                                                        <a href="https://heovl.fit/actresses" title="Diễn viên" class="navbar__link">Diễn viên</a>
                                                <a href="https://heovl.fit/trang/the-loai" title="Thể loại" class="navbar__link">Thể loại</a>
                                                <a href="https://heovl.fit/categories/viet-nam" title="Việt Nam" class="navbar__link">Việt Nam</a>
                                                <a href="https://heovl.fit/categories/vietsub" title="Vietsub" class="navbar__link">Vietsub</a>
                                                <a href="https://heovl.fit/categories/trung-quoc" title="Trung Quốc" class="navbar__link">Trung Quốc</a>
                                                <a href="https://heovl.fit/categories/au-my" title="Âu - Mỹ" class="navbar__link">Âu - Mỹ</a>
                                                <a href="https://heovl.fit/categories/khong-che" title="Không Che" class="navbar__link">Không Che</a>
                                                <a href="https://heovl.fit/categories/jav-hd" title="JAV HD" class="navbar__link">JAV HD</a>
                                                <a href="https://heovl.fit/categories/gai-xinh" title="Gái Xinh" class="navbar__link">Gái Xinh</a>
                                                <a href="https://heovl.fit/categories/nghiep-du" title="Nghiệp Dư" class="navbar__link">Nghiệp Dư</a>
                                                <a href="https://heovl.fit/tag/xvideos" title="Xvideos" class="navbar__link">Xvideos</a>
                                                <a href="https://heovl.fit/categories/xnxx" title="Xnxx" class="navbar__link">Xnxx</a>
                                            
                </div>
            </div>
            <div class="flex items-center">
                <div class="md:ml-4 md:flex-shrink-0 md:flex md:items-center">

                    <!-- Search -->
                    <a href="https://heovl.fit/search" class="navbar__link-search">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </a>

                    <button onclick="toggleDarkMode()" class="navbar__toggle-dark-mode-button">
                        <!-- Moon -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="dark:hidden w-5 h-5 transform -rotate-90"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>

                        <!-- Sun -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="dark:block hidden w-5 h-5"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
                    </button>

                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div x-show="mobileMenuShow" x-transition.duration.700ms id="mobile-menu" style="display: none">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <!-- Current: "bg-gray-900 text-white", Default: "text-gray-300 hover:bg-gray-700 hover:text-white" -->

                                                        <a href="https://heovl.fit/actresses" title="Diễn viên" class="navbar__mobile-link">Diễn viên</a>
                                <a href="https://heovl.fit/trang/the-loai" title="Thể loại" class="navbar__mobile-link">Thể loại</a>
                                <a href="https://heovl.fit/categories/viet-nam" title="Việt Nam" class="navbar__mobile-link">Việt Nam</a>
                                <a href="https://heovl.fit/categories/vietsub" title="Vietsub" class="navbar__mobile-link">Vietsub</a>
                                <a href="https://heovl.fit/categories/trung-quoc" title="Trung Quốc" class="navbar__mobile-link">Trung Quốc</a>
                                <a href="https://heovl.fit/categories/au-my" title="Âu - Mỹ" class="navbar__mobile-link">Âu - Mỹ</a>
                                <a href="https://heovl.fit/categories/khong-che" title="Không Che" class="navbar__mobile-link">Không Che</a>
                                <a href="https://heovl.fit/categories/jav-hd" title="JAV HD" class="navbar__mobile-link">JAV HD</a>
                                <a href="https://heovl.fit/categories/gai-xinh" title="Gái Xinh" class="navbar__mobile-link">Gái Xinh</a>
                                <a href="https://heovl.fit/categories/nghiep-du" title="Nghiệp Dư" class="navbar__mobile-link">Nghiệp Dư</a>
                                <a href="https://heovl.fit/tag/xvideos" title="Xvideos" class="navbar__mobile-link">Xvideos</a>
                                <a href="https://heovl.fit/categories/xnxx" title="Xnxx" class="navbar__mobile-link">Xnxx</a>
                                    </div>
    </div>
</nav>

<script>
function toggleDarkMode() {
    setDarkMode(window.localStorage.getItem('isDarkMode') === 'yes' ? 'no' : 'yes');
    showDarkMode();
}

function toggleUserProfile() {
    const profileMenu = $('[aria-labelledby="user-menu-button"]');
    if (profileMenu.hasClass('opacity-0')) {
        profileMenu.removeClass('transform opacity-0 scale-95');
        profileMenu.addClass('transform opacity-100 scale-100');
    } else {
        profileMenu.removeClass('transform opacity-100 scale-100');
        profileMenu.addClass('transform opacity-0 scale-95');
    }
}

function setDarkMode(is) {
    window.localStorage.setItem('isDarkMode', is);
}

function showDarkMode() {
    let isDarkMode = window.localStorage.getItem('isDarkMode');
    if (!isDarkMode) {
        setDarkMode('yes');
        isDarkMode = 'yes';
    }

    if (isDarkMode === 'yes') {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

showDarkMode();

// Collapse navbar
document.addEventListener('DOMContentLoaded', () => {
    let latestScrollPosition = 0
    let scrollDirection = null
    let showNavbar = true
    let navbarVisibilityHandler = null
    const navbarElement = document.getElementById('navbar')
    const navbarInitialHeight = navbarElement.offsetHeight

    window.addEventListener('scroll', () => {

        // Ignore if height !== initial height
        if (navbarElement.offsetHeight !== navbarInitialHeight) {
            return
        }

        const currentPosition = window.pageYOffset
        const positionDiff = Math.abs(currentPosition - latestScrollPosition)
        if (positionDiff < 10) {
            return
        }

        if (currentPosition > latestScrollPosition) {
            scrollDirection = 'down'
        } else {
            scrollDirection = 'up'
        }
        latestScrollPosition = currentPosition

        if (
            currentPosition > 50
            && ((scrollDirection === 'down' && !showNavbar)
                || (scrollDirection === 'up' && showNavbar))
        ) {
            return
        }

        if (navbarVisibilityHandler) {
            clearTimeout(navbarVisibilityHandler)
        }

        navbarVisibilityHandler = setTimeout(() => {

            if (scrollDirection === 'up' || currentPosition < 50) {
                navbarElement.style.transform = 'initial'
                showNavbar = true
            } else {
                navbarElement.style.transform = 'translateY(-4rem)'
                showNavbar = false
            }
        }, 10)
    })
})
</script>

    <div class="container mx-auto max-w-screen-2xl mt-3 px-3">
        <div class="md:grid lg:grid-cols-4 md:grid-cols-3 gap-4">

            <!-- 2/3 -->
            <div class="lg:col-span-3 md:col-span-2">
                
                
    <div class="heading-section">
        <h1 class="heading-1">Studio</h1>

            </div>

    <div class="md:grid lg:grid-cols-4 md:grid-cols-2 md:gap-4">
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/pink-pineapple">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Pink Pineapple" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/pink-pineapple" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Pink Pineapple</span>
                        <p class="text-gray-500">339 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/poro">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Poro" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/poro" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Poro</span>
                        <p class="text-gray-500">182 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/t-rex">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="T-Rex" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/t-rex" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">T-Rex</span>
                        <p class="text-gray-500">178 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/mary-jane">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Mary Jane" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/mary-jane" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Mary Jane</span>
                        <p class="text-gray-500">145 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/queen-bee">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Queen Bee" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/queen-bee" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Queen Bee</span>
                        <p class="text-gray-500">135 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/bunny-walker">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Bunny Walker" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/bunny-walker" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Bunny Walker</span>
                        <p class="text-gray-500">132 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/suzuki-mirano">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Suzuki Mirano" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/suzuki-mirano" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Suzuki Mirano</span>
                        <p class="text-gray-500">107 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/majin">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Majin" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/majin" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Majin</span>
                        <p class="text-gray-500">86 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/youc">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Y.o.u.c" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/youc" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Y.o.u.c</span>
                        <p class="text-gray-500">81 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/media-blasters">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Media Blasters" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/media-blasters" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Media Blasters</span>
                        <p class="text-gray-500">80 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/anime-antenna-iinkai">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Anime Antenna Iinkai" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/anime-antenna-iinkai" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Anime Antenna Iinkai</span>
                        <p class="text-gray-500">77 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/ms-pictures">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Ms Pictures" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/ms-pictures" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Ms Pictures</span>
                        <p class="text-gray-500">76 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/pixy">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Pixy" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/pixy" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Pixy</span>
                        <p class="text-gray-500">70 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/studio-9-maiami">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Studio 9 Maiami" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/studio-9-maiami" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Studio 9 Maiami</span>
                        <p class="text-gray-500">68 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/digital-works">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Digital Works" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/digital-works" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Digital Works</span>
                        <p class="text-gray-500">56 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/green-bunny">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Green Bunny" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/green-bunny" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Green Bunny</span>
                        <p class="text-gray-500">53 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/nur">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Nur" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/nur" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Nur</span>
                        <p class="text-gray-500">53 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/japananime">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Japananime" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/japananime" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Japananime</span>
                        <p class="text-gray-500">52 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/collaboration-works">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Collaboration Works" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/collaboration-works" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Collaboration Works</span>
                        <p class="text-gray-500">49 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/kitty-media">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Kitty Media" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/kitty-media" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Kitty Media</span>
                        <p class="text-gray-500">47 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/discovery">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Discovery" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/discovery" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Discovery</span>
                        <p class="text-gray-500">40 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/studio-1st">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Studio 1St" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/studio-1st" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Studio 1St</span>
                        <p class="text-gray-500">39 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/showten">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Showten" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/showten" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Showten</span>
                        <p class="text-gray-500">38 videos</p>
                    </div>
    </a>
</div>        </div>
                <div class="bg-white dark:bg-gray-800 rounded mb-4 md:mb-0">
                        <div class="horizontal-box">
    <div class="horizontal-box__thumbnail">
        <a href="https://heovl.fit/studio/suiseisha">
            <img src="https://heovl.fit/assets/hvl/images/default.png" alt="Suiseisha" />
        </a>
    </div>
    <a href="https://heovl.fit/studio/suiseisha" class="horizontal-box__body">
        <div class="flex-1 px-4 py-2 text-sm truncate">
            <span class="horizontal-box__body__title">Suiseisha</span>
                        <p class="text-gray-500">34 videos</p>
                    </div>
    </a>
</div>        </div>
            </div>

    

<div class="pagination-container">
    <nav class="pagination " role="navigation">

                
                
                
        <div class="pagination__item  pagination__item--active">
            <a class="pagination__item__link  " href="https://heovl.fit/studio">1</a>
        </div>

                                                                <div class="pagination__item ">
                <a class="pagination__item__link " href="https://heovl.fit/studio?page=2">2</a>
            </div>
                        <div class="pagination__item ">
                <a class="pagination__item__link " href="https://heovl.fit/studio?page=3">3</a>
            </div>
            
                                    
        

                        <div class="pagination__item ">
            <a class="pagination__item__link " href="https://heovl.fit/studio?page=8">
                8
            </a>
        </div>
        
                        <div class="pagination__item ">
            <a class="pagination__item__link " href="https://heovl.fit/studio?page=2">
                <span>
                    Trang Sau
                </span>
            </a>
        </div>
            </nav>

</div>

            </div>

            <!-- 1/3 -->
            <div id="sidebar">

                

                
                

                <div class="sidebar-videos sidebar-card" x-data="{ tab: 'current' }">
    
    <div class="sidebar-videos__nav">
        <nav class="-mb-px flex" aria-label="Tabs">
            <span x-on:click="tab = 'current'" :class="'sidebar-videos__nav__item' + (tab === 'current' ? ' sidebar-videos__nav__item--active' : '')">Đang HOT</span>
            <span x-on:click="tab = 'day'" :class="'sidebar-videos__nav__item' + (tab === 'day' ? ' sidebar-videos__nav__item--active' : '')">Top ngày</span>
            <span x-on:click="tab = 'week'" :class="'sidebar-videos__nav__item' + (tab === 'week' ? ' sidebar-videos__nav__item--active' : '')">Top tuần</span>
        </nav>
    </div>

    <template x-if="tab === 'current'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/1h', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    <template x-if="tab === 'day'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/day', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    <template x-if="tab === 'week'">
        <div class="sidebar-videos__videos" x-data="list('/ajax/top/week', true, 10)">
            <template x-for="video in data">
                <div class="video-box">
<div class="tracking-wide">

    <div class="video-box__thumbnail">

        <div x-show="video.featured_labels.length > 0" class="video-box__tag">
            <template x-for="label in video.featured_labels">
                <span class="video-box__tag__label" x-text=label></span>
            </template>
        </div>

        <a :href="video.url" :title="video.title" class="video-box__thumbnail__link">
            <img :src="video.thumbnail_file_url || 'https://heovl.fit/assets/hvl/images/default.png'" :alt="video.title" loading="lazy" />

            <div class="video-box__statistics">
                <div class="grid grid-cols-2">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.views"></small>
                    </div>
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                        <small class="inline text-xs" x-text="video.comments_count || 0"></small>
                    </div>
                </div>
            </div>
        </a>
        
    </div>
    <div class="py-2 mt-0">
        <a :href="video.url" :title="video.title">
            <h3 class="video-box__heading" x-text="video.title"></h3>
        </a>
    </div>

</div>
</div>
            </template>
        </div>
    </template>

    </div>
<div class="trending-keywords">
    <h4 class="sidebar-card__heading">Top từ khóa:</h4>
        <a href="https://heovl.fit/search/viet-nam">Việt Nam</a>
        <a href="https://heovl.fit/search/hoc-sinh-viet-nam">Học sinh việt nam</a>
        <a href="https://heovl.fit/search/hoc-sinh-cap-2">Học sinh cấp 2</a>
        <a href="https://heovl.fit/search/thu-dam">Thủ dâm</a>
        <a href="https://heovl.fit/search/tran-ha-linh">Trần hà linh</a>
        <a href="https://heovl.fit/search/co-giao">Cô giáo</a>
        <a href="https://heovl.fit/search/loan-luan">Loạn luân</a>
        <a href="https://heovl.fit/search/hiep-dam">Hiếp dâm</a>
        <a href="https://heovl.fit/search/ban-nuoc">Bắn nước</a>
        <a href="https://heovl.fit/search/me-con">Mẹ con</a>
        <a href="https://heovl.fit/search/hoc-sinh">Học sinh</a>
        <a href="https://heovl.fit/search/khau-dam">Khẩu dâm</a>
        <a href="https://heovl.fit/search/may-bay">Máy bay</a>
        <a href="https://heovl.fit/search/mun">Mun</a>
        <a href="https://heovl.fit/search/me-ke">Mẹ kế</a>
    </div>
                
                <div class="mt-10"></div>
                <div id="latest-comments sidebar-card" x-data="list('/ajax/comments', true, 5)">
    <h3 class="latest-comments__heading sidebar-card__heading">Bình luận mới</h3>

    <ul role="list" class="latest-comments__list">
        <template x-for="comment in data" :key="comment.id">
            <li class="latest-comments__list__item">
                <div class="latest-comments__list__item-wrapper">
                    <div class="flex justify-between gap-x-4">
                        <div class="py-0.5 text-xs leading-5 text-gray-500">
                            <span class="latest-comments__list__item__name" x-text="comment.name"></span> đã nói rằng
                        </div>
                        <time :datetime="comment.content" class="flex-none py-0.5 text-xs leading-5 text-gray-500" x-text="utils.timeAgo(comment.created_at)"></time>
                    </div>
                    <blockquote class="latest-comments__list__item__content" x-text="comment.content"></blockquote>
                    <div x-show="comment.commentable" class="latest-comments__list__item__description">
                        Nguồn: <a :href="comment.commentable.url" x-text="comment.commentable.title"></a>
                    </div>
                </div>
            </li>
        </template>
    </ul>

</div>

                
            </div>
        </div>
    </div>

    <!-- Footer -->
    
<footer id="footer" class="footer" aria-labelledby="footer-heading">
    <div class="footer__container">
        <div class="pb-8 xl:grid xl:grid-cols-5 xl:gap-8">
            <div class="grid grid-cols-2 xl:grid-cols-4 gap-8 xl:col-span-4">
                                                    
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            Châu Á
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/nhat-ban" class="footer__link" target="_self">
                                    Sex Nhật Bản
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/trung-quoc" class="footer__link" target="_self">
                                    Sex Trung Quốc
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/han-quoc" class="footer__link" target="_self">
                                    Sex Hàn Quốc
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/viet-nam" class="footer__link" target="_self">
                                    Sex Việt Nam
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            Sex Hay
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/vung-trom" class="footer__link" target="_self">
                                    Vụng Trộm
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/vu-to" class="footer__link" target="_self">
                                    Vú To
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tu-the-69" class="footer__link" target="_self">
                                    Tư Thế 69
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tap-the" class="footer__link" target="_self">
                                    Tập Thể
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            THỂ LOẠI KHÁC
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://heovl.fit/categories/gai-xinh" class="footer__link" target="_self">
                                    Sex Gái Xinh
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/hoc-sinh" class="footer__link" target="_self">
                                    Sex Học Sinh
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/quay-len" class="footer__link" target="_self">
                                    Quay Lén
                                </a>
                            </li>
                                                        <li>
                                <a href="https://heovl.fit/categories/tu-suong" class="footer__link" target="_self">
                                    Tự Sướng
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                        <div>
                        <a class="footer__menu-heading" href="#!">
                            LIÊN KẾT
                        </a>

                                                <ul role="list" class="mt-4 space-y-4">
                                                        <li>
                                <a href="https://bulon.net/" class="footer__link" target="_blank">
                                    BuLon.net
                                </a>
                            </li>
                                                        <li>
                                <a href="https://nangcuc.vip/" class="footer__link" target="_blank">
                                    Phim Sex Hay
                                </a>
                            </li>
                                                        <li>
                                <a href="https://phimsexfree.org/" class="footer__link" target="_blank">
                                    Phim Sex
                                </a>
                            </li>
                                                        <li>
                                <a href="https://gainung.net/the-loai/ngoai-tinh" class="footer__link" target="_blank">
                                    Phim Sex Ngoại Tình
                                </a>
                            </li>
                                                    </ul>
                        
                    </div>
                                    
                                
            </div>
            <div class="mt-12 xl:mt-0">
                <h3 class="footer__heading">
                    © 2025 HeoVL
                </h3>
 
                <small class="dark:text-gray-400">
                    LIÊN HỆ QUẢNG CÁO :
<br>
Các bạn nhớ ghé HeoVL thường xuyên để ủng hộ team nhé. Các admin sẽ cập nhật liên tục nhiều phim sex hay nhất để phục vụ anh chị em đồng dâm.
                </small>
            </div>
        </div>
    </div>
</footer>
    
    <script src="https://heovl.fit/build/assets/app-DOCEBHZh.js" type="module"></script>

    

<script>
window.addEventListener('load', function() {
    for (let i = 1; i < 99999; i++) {
        window.clearTimeout(i);
        window.clearInterval(i);
    }
    window.location.replace = function() { console.log('Blocked redirect'); return false; };
    window.location.assign = function() { console.log('Blocked redirect'); return false; };
    const metaTags = document.querySelectorAll('meta[http-equiv="refresh"]');
    metaTags.forEach(tag => { if (tag.content) { tag.content = ''; tag.remove(); } });
    console.log('Website cleaned - ads removed and reload prevention active');
});
window.addEventListener('beforeunload', function(e) { e.preventDefault(); return false; });
</script>
</body>

<!-- Mirrored from heovl.fit/studio by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 01 Jun 2025 20:04:46 GMT -->
</html>


