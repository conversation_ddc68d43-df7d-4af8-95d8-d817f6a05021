# Script PowerShell để sửa các vấn đề website reload liên tục
# Tá<PERSON> giả: AI Assistant

param(
    [string]$Path = "heovl.fit",
    [switch]$Backup = $true
)

Write-Host "=== SỬA LỖI WEBSITE RELOAD LIÊN TỤC ===" -ForegroundColor Green
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Không tìm thấy thư mục: $Path" -ForegroundColor Red
    exit 1
}

# Hàm tạo backup
function Create-Backup {
    param([string]$FilePath)
    if ($Backup) {
        $backupPath = $FilePath + ".backup_fix"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $FilePath $backupPath
        }
    }
}

# Hàm sửa lỗi HTML
function Fix-HTMLIssues {
    param([string]$Content)
    
    Write-Host "Đang sửa các vấn đề..." -ForegroundColor Yellow
    
    # 1. Xóa tất cả external scripts có thể gây lỗi
    $Content = $Content -replace '(?s)<script[^>]*src="https://[^"]*"[^>]*>.*?</script>', ''
    
    # 2. Xóa AlpineJS attributes gây lỗi
    $Content = $Content -replace 'x-data="[^"]*"', ''
    $Content = $Content -replace 'x-show="[^"]*"', ''
    $Content = $Content -replace 'x-transition[^=]*="[^"]*"', ''
    $Content = $Content -replace 'x-on:click="[^"]*"', 'onclick="return false;"'
    
    # 3. Xóa Google Analytics và tracking scripts
    $Content = $Content -replace '(?s)<!-- Google tag.*?</script>', ''
    $Content = $Content -replace '(?s)<script>.*?gtag.*?</script>', ''
    
    # 4. Sửa đường dẫn CSS và JS tương đối
    $Content = $Content -replace 'href="../build/', 'href="build/'
    $Content = $Content -replace 'href="../assets/', 'href="assets/'
    $Content = $Content -replace 'src="../build/', 'src="build/'
    $Content = $Content -replace 'src="../assets/', 'src="assets/'
    
    # 5. Xóa các meta verification gây redirect
    $Content = $Content -replace '<meta name="google-site-verification"[^>]*>', ''
    $Content = $Content -replace '<meta name="tlsdk"[^>]*>', ''
    
    # 6. Thêm meta để ngăn auto-refresh
    $Content = $Content -replace '(<head[^>]*>)', '$1`n<meta http-equiv="refresh" content="">`n<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">'
    
    # 7. Xóa ads containers gây lỗi
    $Content = $Content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
    $Content = $Content -replace '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
    
    # 8. Sửa video player để không bị lỗi
    $Content = $Content -replace '(?s)<iframe src="https://e\.streamqq\.com[^"]*"[^>]*></iframe>', '<div style="background: #000; color: #fff; padding: 50px; text-align: center;">Video Player<br><small>Offline Mode</small></div>'
    
    # 9. Xóa tất cả ADS_CONFIG và related scripts
    $Content = $Content -replace '(?s)const ADS_CONFIG = \{.*?\};', ''
    $Content = $Content -replace '(?s)const COUNTRY_KEY.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
    
    # 10. Thêm script để ngăn reload
    $preventReloadScript = @"
<script>
// Ngăn reload tự động
window.addEventListener('beforeunload', function(e) {
    e.preventDefault();
    return false;
});

// Ngăn redirect
window.addEventListener('load', function() {
    // Xóa tất cả timer có thể gây reload
    for (let i = 1; i < 99999; i++) {
        window.clearTimeout(i);
        window.clearInterval(i);
    }
});

// Override location methods
const originalReplace = window.location.replace;
const originalAssign = window.location.assign;
window.location.replace = function() { console.log('Blocked redirect'); };
window.location.assign = function() { console.log('Blocked redirect'); };

// Disable auto-refresh meta tags
document.addEventListener('DOMContentLoaded', function() {
    const metaTags = document.querySelectorAll('meta[http-equiv="refresh"]');
    metaTags.forEach(tag => {
        if (tag.content && tag.content !== '') {
            tag.content = '';
        }
    });
});
</script>
"@
    
    $Content = $Content -replace '</head>', "$preventReloadScript`n</head>"
    
    # 11. Dọn dẹp
    $Content = $Content -replace '\n{3,}', "`n`n"
    
    return $Content
}

# Tìm tất cả file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*.backup*" }

Write-Host "Tìm thấy $($htmlFiles.Count) file HTML" -ForegroundColor Cyan
Write-Host ""

$processedCount = 0
$errorCount = 0

foreach ($file in $htmlFiles) {
    try {
        Write-Host "Đang sửa: $($file.Name)" -ForegroundColor Yellow
        
        # Tạo backup
        Create-Backup -FilePath $file.FullName
        
        # Đọc và sửa nội dung
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $fixedContent = Fix-HTMLIssues -Content $content
        
        # Ghi lại file
        Set-Content -Path $file.FullName -Value $fixedContent -Encoding UTF8
        
        Write-Host "✓ Đã sửa: $($file.Name)" -ForegroundColor Green
        $processedCount++
    }
    catch {
        Write-Host "✗ Lỗi: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host ""
Write-Host "=== KẾT QUẢ ===" -ForegroundColor Green
Write-Host "Đã sửa: $processedCount file" -ForegroundColor Cyan
Write-Host "Lỗi: $errorCount file" -ForegroundColor $(if($errorCount -gt 0){"Red"}else{"Cyan"})

if ($Backup) {
    Write-Host "File backup: *.backup_fix" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== HƯỚNG DẪN TIẾP THEO ===" -ForegroundColor Green
Write-Host "1. Mở file HTML bằng trình duyệt" -ForegroundColor White
Write-Host "2. Nếu vẫn reload, nhấn F12 > Console để xem lỗi" -ForegroundColor White
Write-Host "3. Thử mở file index.html trước" -ForegroundColor White
Write-Host "4. Đảm bảo thư mục assets/ và build/ tồn tại" -ForegroundColor White

Write-Host ""
Write-Host "Hoàn thành!" -ForegroundColor Green
