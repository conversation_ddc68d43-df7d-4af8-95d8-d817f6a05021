# Script xoa ads chinh xac theo file mau da thanh cong
# Dua tren: accelerando-datenshi-tachi-no-sasayaki-2.html
# Tac gia: AI Assistant

param([string]$Path = "heovl.fit")

Write-Host "=== SCRIPT XOA ADS CHINH XAC THEO FILE MAU ===" -ForegroundColor Green
Write-Host "Dua tren file accelerando-datenshi-tachi-no-sasayaki-2.html da thanh cong" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Khong tim thay thu muc: $Path" -ForegroundColor Red
    Read-Host "Nhan Enter de thoat"
    exit 1
}

# Tim file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*backup*" }
Write-Host "Tim thay $($htmlFiles.Count) file HTML" -ForegroundColor Cyan

$confirm = Read-Host "Ban co muon tiep tuc? (y/n)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Da huy bo" -ForegroundColor Yellow
    exit 0
}

$processedCount = 0
$errorCount = 0

foreach ($file in $htmlFiles) {
    try {
        Write-Host "Dang xu ly: $($file.Name)" -ForegroundColor Yellow
        
        # Tao backup truoc khi sua
        $backupPath = $file.FullName + ".backup_exact"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $file.FullName $backupPath
            Write-Host "  Tao backup: $($file.Name).backup_exact" -ForegroundColor Gray
        }
        
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $originalLength = $content.Length
        
        # === XOA CHINH XAC THEO FILE MAU ===
        
        # 1. Xoa tacolo verify code (dong 16-17 trong file mau)
        $content = $content -replace '(?s)<!-- tacolo verify code -->.*?<meta name="tlsdk"[^>]*>', ''
        
        # 2. Xoa Google Analytics & GTM (dong 19-27 trong file mau)
        $content = $content -replace '(?s)<!-- Google tag \(gtag\.js\).*?</script>', ''
        $content = $content -replace '(?s)<script async src="https://www\.googletagmanager\.com/gtag/js.*?</script>', ''
        $content = $content -replace '(?s)<script>.*?window\.dataLayer.*?gtag\(.*?</script>', ''
        
        # 3. Xoa Google site verification (dong 30-31 trong file mau)
        $content = $content -replace '<meta name="google-site-verification"[^>]*>', ''
        
        # 4. Xoa ads containers (dong 263-265 trong file mau)
        $content = $content -replace '(?s)<div class="ad-place ads--under-navbar">.*?</div>', ''
        $content = $content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
        
        # 5. Xoa under player ads (dong 355-368 trong file mau)
        $content = $content -replace '(?s)<!-- Under video players ads -->.*?<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
        $content = $content -replace '(?s)<style>.*?\.responsive-container.*?height: 156px.*?</style>', ''
        
        # 6. Xoa obfuscated ads script (dong 1383-1385 trong file mau)
        $content = $content -replace '(?s)<script>.*?var [A-Z]{10,}.*?qpSeg.*?</script>', ''
        
        # 7. Xoa script ads tu ads.x-cdn.org (dong 1386 trong file mau)
        $content = $content -replace '(?s)<script src="[^"]*ads\.x-cdn\.org[^"]*"[^>]*>.*?</script>', ''
        
        # 8. Xoa ADS_CONFIG object (dong 1387-1450 trong file mau)
        $content = $content -replace '(?s)const ADS_CONFIG = \{.*?underPlayerBanner:.*?\},.*?\};', ''
        
        # 9. Xoa country detection va ads functions (dong 1452-1493 trong file mau)
        $content = $content -replace '(?s)const COUNTRY_KEY = "userCountry";.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
        
        # 10. Sua video player de xoa adTag parameters
        $content = $content -replace 'data-source="([^"]*?)&amp;adTag=[^"]*"', 'data-source="$1"'
        $content = $content -replace 'data-source="([^"]*?)\?adTag=[^"]*"', 'data-source="$1"'
        $content = $content -replace 'data-source="([^"]*?)&adTag=[^"]*"', 'data-source="$1"'
        
        # 11. Sua function setPlayerSource de loai bo ads (theo dong 341-351 trong file mau)
        $oldPlayerPattern = 'playerWrapper\.innerHTML = `<iframe src="\` \+ url \+ `"'
        $newPlayerCode = @'
// Remove ads parameters from URL
            let cleanUrl = url;
            if (cleanUrl.includes('adTag=')) {
                cleanUrl = cleanUrl.split('&adTag=')[0];
            }
            if (cleanUrl.includes('?adTag=')) {
                cleanUrl = cleanUrl.split('?adTag=')[0];
            }

            console.log('Loading video without ads:', cleanUrl);
            playerWrapper.innerHTML = `<iframe src="` + cleanUrl + `"
'@
        $content = $content -replace $oldPlayerPattern, $newPlayerCode
        
        # 12. Don dep dong trong
        $content = $content -replace '\n{3,}', "`n`n"
        
        # Kiem tra xem co thay doi gi khong
        $newLength = $content.Length
        $removedBytes = $originalLength - $newLength
        
        if ($removedBytes -gt 0) {
            # Ghi lai file
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            $removedKB = [math]::Round($removedBytes / 1KB, 2)
            Write-Host "  Da xoa ads: $removedKB KB" -ForegroundColor Green
            $processedCount++
        } else {
            Write-Host "  Khong co ads de xoa" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  Loi: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

# Xoa thu muc ads neu co
$adsFolder = Join-Path $Path "ads.x-cdn.org"
if (Test-Path $adsFolder) {
    try {
        Remove-Item $adsFolder -Recurse -Force
        Write-Host ""
        Write-Host "Da xoa thu muc ads.x-cdn.org" -ForegroundColor Green
    }
    catch {
        Write-Host "Khong the xoa thu muc ads" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== KET QUA ===" -ForegroundColor Green
Write-Host "Tong file: $($htmlFiles.Count)" -ForegroundColor Cyan
Write-Host "Da xu ly: $processedCount file" -ForegroundColor Green
Write-Host "Loi: $errorCount file" -ForegroundColor $(if($errorCount -gt 0){"Red"}else{"Cyan"})
Write-Host "File backup: *.backup_exact" -ForegroundColor Yellow

Write-Host ""
Write-Host "=== TUONG TU FILE MAU ===" -ForegroundColor Green
Write-Host "accelerando-datenshi-tachi-no-sasayaki-2.html" -ForegroundColor White
Write-Host "- Da xoa tacolo verify code" -ForegroundColor White
Write-Host "- Da xoa Google Analytics & GTM" -ForegroundColor White
Write-Host "- Da xoa Google site verification" -ForegroundColor White
Write-Host "- Da xoa ads containers" -ForegroundColor White
Write-Host "- Da xoa under player ads" -ForegroundColor White
Write-Host "- Da xoa obfuscated ads scripts" -ForegroundColor White
Write-Host "- Da xoa ADS_CONFIG object" -ForegroundColor White
Write-Host "- Da xoa country detection" -ForegroundColor White
Write-Host "- Da sua video player loai bo adTag" -ForegroundColor White
Write-Host "- Da sua function setPlayerSource" -ForegroundColor White

Write-Host ""
Write-Host "Website se giong nhu file mau - khong reload, it ads!" -ForegroundColor Green
Read-Host "Nhan Enter de thoat"
