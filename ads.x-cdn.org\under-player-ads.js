(function () {
    const CONFIG = ADS_CONFIG.underPlayerBanner;
  
    if (!CONFIG.enabled) return;
  
    function injectCSS() {
      const css = `
        #underPlayerAdsContainer {
          width: 100%; /* <PERSON><PERSON><PERSON> rộng full */
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 2px;
          position: relative;
          overflow: hidden; 
        }
        .under-player-banner {
          display: block;
          height: 35px;
          aspect-ratio: 728/90;
          margin: 0 auto;
          object-fit: cover;
          max-width: 728px;
        }
        @media screen and (min-width: 768px) {
          .under-player-banner {
            height: 50px;
            max-height: 80px;
          }
        }
      `;
      const style = document.createElement("style");
      style.textContent = css;
      document.head.appendChild(style);
    }
  
    function fillUnderPlayerAds() {
      const container = document.getElementById("underPlayerAdsContainer");
  
      if (!container) {
        console.error("Under Player Ads container not found!");
        return;
      }
  
      CONFIG.banners.forEach((ad) => {
        const banner = document.createElement("img");
		banner.setAttribute("loading", "lazy");
        banner.src = ad.img;
        banner.alt = "Under Player Banner";
        banner.className = "under-player-banner";
        banner.onclick = () => window.open(ad.link);
        container.appendChild(banner);
      });
    }
  
    injectCSS();
    fillUnderPlayerAds();
  })();