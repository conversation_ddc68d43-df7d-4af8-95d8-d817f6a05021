# Script Xóa Quảng Cáo Website

Bộ script PowerShell để xóa tất cả quảng cáo khỏi các file HTML đã clone về từ website.

## 📁 Các File Script

### 1. `Remove-Ads-Simple.ps1` (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị cho người mới)
- <PERSON><PERSON><PERSON> đơ<PERSON> g<PERSON>, d<PERSON> sử dụng
- Chỉ cần nhấp chuột phải và chọn "Run with PowerShell"
- Tự động tạo backup
- Gia<PERSON> diện thân thiện

### 2. `Remove-Ads.ps1` (<PERSON><PERSON> bản)
- <PERSON>ript c<PERSON> bản với các tùy chọn parameters
- Phù hợp cho người có kinh nghiệm PowerShell

### 3. `Remove-Ads-Advanced.ps1` (Nâng cao)
- <PERSON><PERSON><PERSON><PERSON> tùy chọn nâng cao
- Chế độ DRY RUN để kiểm tra trước
- <PERSON><PERSON>o cáo chi tiết
- <PERSON><PERSON> tiến trình

## 🚀 Cách Sử Dụng

### Phương pháp 1: <PERSON><PERSON><PERSON> g<PERSON> (Khuyến nghị)

1. Đặt file `Remove-Ads-Simple.ps1` vào cùng thư mục với folder `heovl.fit`
2. Nhấp chuột phải vào file `Remove-Ads-Simple.ps1`
3. Chọn "Run with PowerShell"
4. Làm theo hướng dẫn trên màn hình

### Phương pháp 2: Command Line

```powershell
# Script cơ bản
.\Remove-Ads.ps1

# Script nâng cao với các tùy chọn
.\Remove-Ads-Advanced.ps1 -Path "heovl.fit" -Verbose -ShowProgress

# Chế độ kiểm tra (không thay đổi file)
.\Remove-Ads-Advanced.ps1 -DryRun

# Không tạo backup
.\Remove-Ads-Advanced.ps1 -Backup:$false
```

## 🎯 Các Loại Quảng Cáo Được Xóa

✅ **Google Analytics & GTM Scripts**
- Tracking codes
- gtag.js scripts
- Google Tag Manager

✅ **Google Site Verification**
- Meta verification tags
- Tacolo verification codes

✅ **Ad Placement Containers**
- `<div class="ad-place">` elements
- Under player ad containers
- Responsive ad containers

✅ **JavaScript Ad Configurations**
- `ADS_CONFIG` objects
- Country detection scripts
- Ad loading functions

✅ **External Ad Scripts**
- Scripts từ `ads.x-cdn.org`
- Popup ad scripts
- Banner ad scripts

✅ **Ad Folders**
- Thư mục `ads.x-cdn.org` và nội dung

## ⚙️ Tùy Chọn Nâng Cao

### Remove-Ads-Advanced.ps1 Parameters:

| Parameter | Mô tả | Mặc định |
|-----------|-------|----------|
| `-Path` | Đường dẫn thư mục website | "heovl.fit" |
| `-Backup` | Tạo backup file gốc | `$true` |
| `-Verbose` | Ghi log chi tiết | `$false` |
| `-DryRun` | Chế độ kiểm tra (không thay đổi) | `$false` |
| `-RemoveAdsFolder` | Xóa thư mục ads | `$true` |
| `-LogFile` | File log | "ads-removal.log" |
| `-ExcludeFiles` | Loại trừ file cụ thể | `@()` |
| `-ShowProgress` | Hiển thị thanh tiến trình | `$true` |

### Ví dụ sử dụng nâng cao:

```powershell
# Kiểm tra trước khi thực hiện
.\Remove-Ads-Advanced.ps1 -DryRun -Verbose

# Xử lý với log chi tiết
.\Remove-Ads-Advanced.ps1 -Verbose -LogFile "my-log.txt"

# Loại trừ một số file
.\Remove-Ads-Advanced.ps1 -ExcludeFiles @("index.html", "special.html")

# Không tạo backup (cẩn thận!)
.\Remove-Ads-Advanced.ps1 -Backup:$false
```

## 🔒 An Toàn & Backup

### Backup Tự Động
- Tất cả script đều tạo backup mặc định
- File backup có extension `.backup` hoặc `.backup_timestamp`
- Để khôi phục: đổi tên file `.backup` thành `.html`

### Khôi Phục File Gốc
```powershell
# Khôi phục tất cả file từ backup
Get-ChildItem -Recurse -Filter "*.backup" | ForEach-Object {
    $originalFile = $_.FullName -replace '\.backup.*$', ''
    Copy-Item $_.FullName $originalFile -Force
}
```

## 📊 Kết Quả Mong Đợi

Sau khi chạy script:
- ✅ Website load nhanh hơn (giảm 20-50% dung lượng)
- ✅ Không còn popup quảng cáo
- ✅ Không còn banner quảng cáo
- ✅ Không còn tracking scripts
- ✅ Giao diện sạch sẽ hơn

## ⚠️ Lưu Ý Quan Trọng

1. **Luôn tạo backup** trước khi chạy script
2. **Kiểm tra kết quả** sau khi xử lý
3. **Sử dụng DryRun** để kiểm tra trước
4. Script chỉ xóa quảng cáo, không ảnh hưởng nội dung chính
5. Một số chức năng website có thể bị ảnh hưởng nếu phụ thuộc vào ads scripts

## 🐛 Xử Lý Lỗi

### Lỗi thường gặp:

**"Execution Policy"**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**"File đang được sử dụng"**
- Đóng tất cả trình duyệt
- Đóng text editor đang mở file HTML

**"Không tìm thấy thư mục"**
- Đảm bảo script nằm cùng thư mục với folder website
- Kiểm tra tên thư mục chính xác

## 📈 Thống Kê

Script có thể xử lý:
- ✅ Hàng nghìn file HTML cùng lúc
- ✅ File có dung lượng lớn (>10MB)
- ✅ Cấu trúc thư mục phức tạp
- ✅ Encoding UTF-8, UTF-16

## 🤝 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra file log (nếu có)
2. Chạy với tùy chọn `-Verbose`
3. Sử dụng `-DryRun` để kiểm tra
4. Đảm bảo có quyền ghi file trong thư mục

---

**Chúc bạn sử dụng thành công! 🎉**
