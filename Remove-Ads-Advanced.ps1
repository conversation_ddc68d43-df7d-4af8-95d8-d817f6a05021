# Script PowerShell nâng cao để xóa quảng cáo khỏi các file HTML
# Tác giả: AI Assistant
# Mô tả: <PERSON><PERSON><PERSON> tất cả các đoạn code quảng cáo với nhiều tùy chọn nâng cao

[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)]
    [string]$Path = "heovl.fit",
    
    [Parameter(Mandatory=$false)]
    [switch]$Backup = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveAdsFolder = $true,
    
    [Parameter(Mandatory=$false)]
    [string]$LogFile = "ads-removal.log",
    
    [Parameter(Mandatory=$false)]
    [string[]]$ExcludeFiles = @(),
    
    [Parameter(Mandatory=$false)]
    [switch]$ShowProgress = $true
)

# Thiết lập encoding UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Hàm ghi log với màu sắc
function Write-ColorLog {
    param(
        [string]$Message, 
        [string]$Level = "INFO",
        [ConsoleColor]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Xác định màu dựa trên level
    switch ($Level) {
        "SUCCESS" { $Color = "Green" }
        "ERROR" { $Color = "Red" }
        "WARNING" { $Color = "Yellow" }
        "INFO" { $Color = "Cyan" }
        default { $Color = "White" }
    }
    
    Write-Host $logMessage -ForegroundColor $Color
    
    if ($Verbose -and $LogFile) {
        Add-Content -Path $LogFile -Value $logMessage -Encoding UTF8
    }
}

# Hàm tạo backup với timestamp
function Create-BackupWithTimestamp {
    param([string]$FilePath)
    
    if ($Backup -and -not $DryRun) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupPath = $FilePath + ".backup_$timestamp"
        
        if (-not (Test-Path $backupPath)) {
            Copy-Item $FilePath $backupPath
            Write-ColorLog "Backup created: $backupPath" "SUCCESS"
        }
    }
}

# Hàm xóa quảng cáo nâng cao
function Remove-AdsAdvanced {
    param([string]$Content)
    
    $originalLength = $Content.Length
    
    # Danh sách các pattern quảng cáo cần xóa
    $adPatterns = @(
        # Google Analytics & GTM
        '(?s)<!-- Google tag \(gtag\.js\).*?</script>',
        '(?s)<script async src="https://www\.googletagmanager\.com/gtag/js.*?</script>',
        '(?s)<script>.*?gtag\(.*?</script>',
        
        # Google verification
        '<meta name="google-site-verification"[^>]*>',
        
        # Tacolo verification
        '<meta name="tlsdk"[^>]*>',
        
        # Ad placement containers
        '(?s)<div class="ad-place[^"]*">.*?</div>',
        '(?s)<div[^>]*class="[^"]*ads[^"]*"[^>]*>.*?</div>',
        
        # Under player ads
        '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>',
        '(?s)<style>.*?\.responsive-container.*?</style>',
        
        # ADS_CONFIG và related
        '(?s)const ADS_CONFIG = \{.*?\};',
        '(?s)var ADS_CONFIG = \{.*?\};',
        '(?s)window\.ADS_CONFIG = \{.*?\};',
        
        # Ads functions
        '(?s)function addAdsScripts\(\).*?\}',
        '(?s)function fetchCountryAndSaveToLocalStorage\(\).*?\}',
        '(?s)function checkAndAddAdsScripts\(\).*?\}',
        
        # Country detection for ads
        '(?s)const COUNTRY_KEY = "userCountry";.*?checkAndAddAdsScripts\(\);.*?\}\);',
        
        # External ads scripts
        '(?s)<script[^>]*src="[^"]*ads\.x-cdn\.org[^"]*"[^>]*>.*?</script>',
        '(?s)<script[^>]*src="[^"]*googletagmanager[^"]*"[^>]*>.*?</script>',
        
        # Popup ads
        '(?s)<!-- popup ads -->.*?<!-- /popup ads -->',
        
        # Banner ads
        '(?s)<!-- banner ads -->.*?<!-- /banner ads -->',
        
        # Inline ads
        '(?s)<ins class="adsbygoogle".*?</ins>',
        '(?s)<script.*?adsbygoogle.*?</script>'
    )
    
    # Áp dụng từng pattern
    foreach ($pattern in $adPatterns) {
        $Content = $Content -replace $pattern, ''
    }
    
    # Dọn dẹp các dòng trống thừa
    $Content = $Content -replace '(?m)^\s*$\n', ''
    $Content = $Content -replace '\n{3,}', "`n`n"
    $Content = $Content -replace '(?s)\s*</head>', "`n</head>"
    $Content = $Content -replace '(?s)\s*</body>', "`n</body>"
    
    $newLength = $Content.Length
    $removedBytes = $originalLength - $newLength
    
    return @{
        Content = $Content
        RemovedBytes = $removedBytes
    }
}

# Hàm kiểm tra file có chứa quảng cáo
function Test-HasAds {
    param([string]$Content)
    
    $adIndicators = @(
        'ads\.x-cdn\.org',
        'ADS_CONFIG',
        'google-site-verification',
        'underPlayerAdsContainer',
        'ad-place',
        'googletagmanager',
        'gtag\(',
        'adsbygoogle'
    )
    
    foreach ($indicator in $adIndicators) {
        if ($Content -match $indicator) {
            return $true
        }
    }
    
    return $false
}

# Main script execution
Write-ColorLog "=== SCRIPT XÓA QUẢNG CÁO WEBSITE ===" "INFO"
Write-ColorLog "Đường dẫn: $Path" "INFO"
Write-ColorLog "Chế độ: $(if($DryRun){'DRY RUN (không thay đổi file)'}else{'THỰC THI'})" "INFO"

if (-not (Test-Path $Path)) {
    Write-ColorLog "Thư mục không tồn tại: $Path" "ERROR"
    exit 1
}

# Tìm tất cả file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | 
    Where-Object { 
        $_.Name -notlike "*.backup*" -and 
        $_.Name -notin $ExcludeFiles 
    }

Write-ColorLog "Tìm thấy $($htmlFiles.Count) file HTML" "INFO"

if ($htmlFiles.Count -eq 0) {
    Write-ColorLog "Không tìm thấy file HTML nào để xử lý" "WARNING"
    exit 0
}

# Khởi tạo counters
$processedCount = 0
$errorCount = 0
$totalRemovedBytes = 0
$filesWithAds = 0

# Xử lý từng file
for ($i = 0; $i -lt $htmlFiles.Count; $i++) {
    $file = $htmlFiles[$i]
    
    if ($ShowProgress) {
        $percentComplete = [math]::Round(($i / $htmlFiles.Count) * 100, 2)
        Write-Progress -Activity "Đang xử lý file HTML" -Status "File: $($file.Name)" -PercentComplete $percentComplete
    }
    
    try {
        Write-ColorLog "Đang xử lý: $($file.Name)" "INFO"
        
        # Đọc nội dung file
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Kiểm tra có quảng cáo không
        if (Test-HasAds -Content $content) {
            $filesWithAds++
            
            if (-not $DryRun) {
                # Tạo backup
                Create-BackupWithTimestamp -FilePath $file.FullName
            }
            
            # Xóa quảng cáo
            $result = Remove-AdsAdvanced -Content $content
            $totalRemovedBytes += $result.RemovedBytes
            
            if (-not $DryRun) {
                # Ghi lại file
                Set-Content -Path $file.FullName -Value $result.Content -Encoding UTF8
            }
            
            $sizeReduction = [math]::Round($result.RemovedBytes / 1KB, 2)
            Write-ColorLog "Đã xóa quảng cáo từ: $($file.Name) (giảm ${sizeReduction}KB)" "SUCCESS"
            $processedCount++
        } else {
            Write-ColorLog "Không có quảng cáo trong: $($file.Name)" "INFO"
        }
    }
    catch {
        Write-ColorLog "Lỗi khi xử lý file $($file.FullName): $($_.Exception.Message)" "ERROR"
        $errorCount++
    }
}

if ($ShowProgress) {
    Write-Progress -Activity "Đang xử lý file HTML" -Completed
}

# Xóa thư mục ads nếu được yêu cầu
if ($RemoveAdsFolder -and -not $DryRun) {
    $adsDir = Join-Path $Path "ads.x-cdn.org"
    if (Test-Path $adsDir) {
        try {
            Remove-Item $adsDir -Recurse -Force
            Write-ColorLog "Đã xóa thư mục quảng cáo: $adsDir" "SUCCESS"
        }
        catch {
            Write-ColorLog "Không thể xóa thư mục quảng cáo: $($_.Exception.Message)" "ERROR"
        }
    }
}

# Báo cáo kết quả
Write-ColorLog "=== KẾT QUẢ ===" "INFO"
Write-ColorLog "Tổng số file HTML: $($htmlFiles.Count)" "INFO"
Write-ColorLog "File có quảng cáo: $filesWithAds" "INFO"
Write-ColorLog "File đã xử lý: $processedCount" "SUCCESS"
Write-ColorLog "File lỗi: $errorCount" $(if($errorCount -gt 0){"ERROR"}else{"INFO"})
Write-ColorLog "Tổng dung lượng quảng cáo đã xóa: $([math]::Round($totalRemovedBytes / 1KB, 2))KB" "SUCCESS"

if ($DryRun) {
    Write-ColorLog "Chế độ DRY RUN - Không có file nào bị thay đổi" "WARNING"
}

if ($Backup -and $processedCount -gt 0 -and -not $DryRun) {
    Write-ColorLog "Các file backup được tạo với timestamp" "INFO"
}

Write-ColorLog "Script hoàn thành!" "SUCCESS"
