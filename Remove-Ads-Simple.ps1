# Script PowerShell đơn giản để xóa quảng cáo
# Sử dụng: Nh<PERSON><PERSON> chuột phải vào file này và chọn "Run with PowerShell"

Write-Host "=== SCRIPT XÓA QUẢNG CÁO WEBSITE ===" -ForegroundColor Green
Write-Host ""

# Kiểm tra thư mục heovl.fit
$websiteFolder = "heovl.fit"
if (-not (Test-Path $websiteFolder)) {
    Write-Host "Không tìm thấy thư mục '$websiteFolder' trong thư mục hiện tại!" -ForegroundColor Red
    Write-Host "Vui lòng đảm bảo script này nằm cùng thư mục với folder '$websiteFolder'" -ForegroundColor Yellow
    Read-Host "Nhấn Enter để thoát"
    exit
}

Write-Host "Tìm thấy thư mục website: $websiteFolder" -ForegroundColor Green

# Tìm tất cả file HTML
$htmlFiles = Get-ChildItem -Path $websiteFolder -Recurse -Filter "*.html"
Write-Host "Tìm thấy $($htmlFiles.Count) file HTML" -ForegroundColor Cyan

# Hỏi người dùng có muốn tiếp tục không
Write-Host ""
$confirm = Read-Host "Bạn có muốn tiếp tục xóa quảng cáo? (y/n)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Đã hủy bỏ." -ForegroundColor Yellow
    Read-Host "Nhấn Enter để thoát"
    exit
}

Write-Host ""
Write-Host "Đang xử lý..." -ForegroundColor Yellow

$processedCount = 0
$errorCount = 0

foreach ($file in $htmlFiles) {
    try {
        # Đọc nội dung file
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Kiểm tra có quảng cáo không
        if ($content -match "(ads\.x-cdn\.org|ADS_CONFIG|google-site-verification|underPlayerAdsContainer)") {
            
            # Tạo backup
            $backupPath = $file.FullName + ".backup"
            if (-not (Test-Path $backupPath)) {
                Copy-Item $file.FullName $backupPath
            }
            
            # Xóa các đoạn quảng cáo
            $cleanContent = $content
            
            # Xóa Google Analytics
            $cleanContent = $cleanContent -replace '(?s)<!-- Google tag \(gtag\.js\).*?</script>', ''
            $cleanContent = $cleanContent -replace '(?s)<script async src="https://www\.googletagmanager\.com/gtag/js.*?</script>', ''
            
            # Xóa Google verification
            $cleanContent = $cleanContent -replace '<meta name="google-site-verification"[^>]*>', ''
            
            # Xóa tacolo verify
            $cleanContent = $cleanContent -replace '<meta name="tlsdk"[^>]*>', ''
            
            # Xóa ad containers
            $cleanContent = $cleanContent -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
            $cleanContent = $cleanContent -replace '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
            
            # Xóa ADS_CONFIG
            $cleanContent = $cleanContent -replace '(?s)const ADS_CONFIG = \{.*?\};', ''
            
            # Xóa ads scripts
            $cleanContent = $cleanContent -replace '(?s)const COUNTRY_KEY = "userCountry";.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
            
            # Xóa responsive container style
            $cleanContent = $cleanContent -replace '(?s)<style>.*?\.responsive-container.*?</style>', ''
            
            # Dọn dẹp dòng trống
            $cleanContent = $cleanContent -replace '\n{3,}', "`n`n"
            
            # Ghi lại file
            Set-Content -Path $file.FullName -Value $cleanContent -Encoding UTF8
            
            Write-Host "✓ Đã xóa quảng cáo từ: $($file.Name)" -ForegroundColor Green
            $processedCount++
        }
    }
    catch {
        Write-Host "✗ Lỗi khi xử lý: $($file.Name)" -ForegroundColor Red
        $errorCount++
    }
}

# Xóa thư mục ads
$adsFolder = Join-Path $websiteFolder "ads.x-cdn.org"
if (Test-Path $adsFolder) {
    try {
        Remove-Item $adsFolder -Recurse -Force
        Write-Host "✓ Đã xóa thư mục quảng cáo" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Không thể xóa thư mục quảng cáo" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== KẾT QUẢ ===" -ForegroundColor Green
Write-Host "Đã xử lý: $processedCount file" -ForegroundColor Cyan
Write-Host "Lỗi: $errorCount file" -ForegroundColor $(if($errorCount -gt 0){"Red"}else{"Cyan"})
Write-Host "Các file gốc được backup với extension .backup" -ForegroundColor Yellow
Write-Host ""
Write-Host "Hoàn thành! Website đã được làm sạch quảng cáo." -ForegroundColor Green

Read-Host "Nhấn Enter để thoát"
