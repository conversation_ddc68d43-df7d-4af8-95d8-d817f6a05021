# Script don gian sua loi website reload
# Tac gia: AI Assistant

param([string]$Path = "heovl.fit")

Write-Host "=== SUA LOI WEBSITE RELOAD ===" -ForegroundColor Green
Write-Host "Dang xu ly thu muc: $Path" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Khong tim thay thu muc: $Path" -ForegroundColor Red
    Read-Host "Nhan Enter de thoat"
    exit 1
}

# Tao backup
Write-Host "Tao backup..." -ForegroundColor Yellow
$backupFolder = "${Path}_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
try {
    Copy-Item -Path $Path -Destination $backupFolder -Recurse -Force
    Write-Host "Backup thanh cong: $backupFolder" -ForegroundColor Green
} catch {
    Write-Host "Khong the tao backup" -ForegroundColor Yellow
}

# Xoa thu muc ads
Write-Host ""
Write-Host "Xoa thu muc quang cao..." -ForegroundColor Yellow
$adsFolder = Join-Path $Path "ads.x-cdn.org"
if (Test-Path $adsFolder) {
    Remove-Item $adsFolder -Recurse -Force
    Write-Host "Da xoa thu muc ads" -ForegroundColor Green
}

# Tao thu muc can thiet
Write-Host ""
Write-Host "Tao cau truc thu muc..." -ForegroundColor Yellow
$folders = @("assets\hvl\images", "build\assets")
foreach ($folder in $folders) {
    $folderPath = Join-Path $Path $folder
    if (-not (Test-Path $folderPath)) {
        New-Item -Path $folderPath -ItemType Directory -Force | Out-Null
        Write-Host "Tao thu muc: $folder" -ForegroundColor Green
    }
}

# Tao CSS co ban
$cssContent = @"
body{font-family:Arial,sans-serif;margin:0;padding:0;background:#1a1a1a;color:#fff}
.container{max-width:1200px;margin:0 auto;padding:20px}
.video-player{width:100%;height:400px;background:#000;border:1px solid #333;margin:20px 0}
.navbar__link{color:#fff;text-decoration:none;margin:0 10px;padding:5px 10px}
.heading-1{font-size:24px;margin:20px 0;color:#fff}
.breadcrumb{list-style:none;padding:0;margin:10px 0}
.breadcrumb__item{display:inline;margin-right:10px}
.featured-list__desktop__list{list-style:none;padding:0}
.dark{background:#1a1a1a;color:#fff}
"@

$cssPath = Join-Path $Path "build\assets\app-Cb5tGUTM.css"
Set-Content -Path $cssPath -Value $cssContent -Encoding UTF8

$themePath = Join-Path $Path "assets\hvl\theme0345.css"
Set-Content -Path $themePath -Value $cssContent -Encoding UTF8

Write-Host "Tao file CSS co ban" -ForegroundColor Green

# Sua file HTML
Write-Host ""
Write-Host "Sua file HTML..." -ForegroundColor Yellow

$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*backup*" }
$processedCount = 0

foreach ($file in $htmlFiles) {
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Xoa external scripts
        $content = $content -replace '(?s)<script[^>]*src="https://[^"]*"[^>]*>.*?</script>', ''
        
        # Xoa AlpineJS
        $content = $content -replace 'x-data="[^"]*"', ''
        $content = $content -replace 'x-show="[^"]*"', 'style="display:block"'
        $content = $content -replace 'x-transition[^=]*="[^"]*"', ''
        $content = $content -replace 'x-on:click="[^"]*"', 'onclick="return false;"'
        
        # Xoa Google Analytics
        $content = $content -replace '(?s)<!-- Google tag.*?</script>', ''
        $content = $content -replace '(?s)<script>.*?gtag.*?</script>', ''
        
        # Xoa meta verification
        $content = $content -replace '<meta name="google-site-verification"[^>]*>', ''
        $content = $content -replace '<meta name="tlsdk"[^>]*>', ''
        
        # Sua duong dan CSS/JS
        $content = $content -replace 'href="../build/', 'href="build/'
        $content = $content -replace 'href="../assets/', 'href="assets/'
        $content = $content -replace 'src="../build/', 'src="build/'
        $content = $content -replace 'src="../assets/', 'src="assets/'
        
        # Xoa ads containers
        $content = $content -replace '(?s)<div class="ad-place[^"]*">.*?</div>', ''
        $content = $content -replace '(?s)<div id="underPlayerAdsContainer"[^>]*>.*?</div>', ''
        $content = $content -replace '(?s)<style>.*?\.responsive-container.*?</style>', ''
        
        # Xoa ADS_CONFIG
        $content = $content -replace '(?s)const ADS_CONFIG = \{.*?\};', ''
        $content = $content -replace '(?s)const COUNTRY_KEY.*?checkAndAddAdsScripts\(\);.*?\}\);', ''
        
        # Sua video player
        $content = $content -replace '(?s)<iframe src="https://[^"]*streamqq[^"]*"[^>]*></iframe>', '<div style="background:#000;color:#fff;padding:50px;text-align:center;border:1px solid #333;">Video Player - Offline Mode</div>'
        
        # Them script ngan reload
        $preventScript = @"
<script>
window.addEventListener('load', function() {
    for (let i = 1; i < 99999; i++) {
        window.clearTimeout(i);
        window.clearInterval(i);
    }
});
window.location.replace = function() { console.log('Blocked redirect'); };
window.location.assign = function() { console.log('Blocked redirect'); };
</script>
"@
        
        if ($content -notmatch 'Blocked redirect') {
            $content = $content -replace '</head>', "$preventScript`n</head>"
        }
        
        # Don dep
        $content = $content -replace '\n{3,}', "`n`n"
        
        # Ghi lai file
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        $processedCount++
        
        if ($processedCount % 50 -eq 0) {
            Write-Host "  Da xu ly: $processedCount file..." -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "Loi file: $($file.Name)" -ForegroundColor Red
    }
}

Write-Host "Da sua $processedCount file HTML" -ForegroundColor Green

# Kiem tra file index
Write-Host ""
Write-Host "Kiem tra file index..." -ForegroundColor Yellow

$indexPath = Join-Path $Path "index.html"
if (-not (Test-Path $indexPath)) {
    $simpleIndex = @"
<!DOCTYPE html>
<html>
<head>
    <title>Website Offline</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="build/assets/app-Cb5tGUTM.css">
</head>
<body>
    <div class="container">
        <h1>Website da duoc lam sach</h1>
        <p>Website da duoc xoa quang cao va sua loi reload.</p>
        <p>Duyet cac thu muc:</p>
        <ul>
            <li><a href="videos/">Videos</a></li>
            <li><a href="categories/">Categories</a></li>
        </ul>
    </div>
</body>
</html>
"@
    Set-Content -Path $indexPath -Value $simpleIndex -Encoding UTF8
    Write-Host "Tao file index.html moi" -ForegroundColor Green
}

# Ket qua
Write-Host ""
Write-Host "HOAN THANH!" -ForegroundColor Green
Write-Host ""
Write-Host "Thong ke:" -ForegroundColor Cyan
Write-Host "  Da sua: $processedCount file HTML" -ForegroundColor White
Write-Host "  Backup tai: $backupFolder" -ForegroundColor White
Write-Host "  Da xoa quang cao va sua loi reload" -ForegroundColor White
Write-Host ""
Write-Host "Cach su dung:" -ForegroundColor Cyan
Write-Host "  1. Mo file index.html bang trinh duyet" -ForegroundColor White
Write-Host "  2. Hoac mo bat ky file HTML nao trong thu muc videos/" -ForegroundColor White
Write-Host "  3. Website se khong con reload lien tuc" -ForegroundColor White
Write-Host ""
Write-Host "Luu y:" -ForegroundColor Yellow
Write-Host "  Video player se hien thi 'Offline Mode'" -ForegroundColor White
Write-Host "  Mot so chuc nang co the khong hoat dong do offline" -ForegroundColor White
Write-Host "  De khoi phuc, su dung thu muc backup" -ForegroundColor White

Read-Host "`nNhan Enter de thoat"
