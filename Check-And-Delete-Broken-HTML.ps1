# Script kiem tra va xoa file HTML bi hu format
# Tac gia: AI Assistant

param(
    [string]$Path = "heovl.fit",
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

Write-Host "=== KIEM TRA VA XOA FILE HTML BI HU ===" -ForegroundColor Green
Write-Host "Thu muc: $Path" -ForegroundColor Cyan
if ($DryRun) {
    Write-Host "CHE DO KIEM TRA - KHONG XOA FILE" -ForegroundColor Yellow
} else {
    Write-Host "CHE DO XOA - SE XOA FILE BI HU" -ForegroundColor Red
}
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Khong tim thay thu muc: $Path" -ForegroundColor Red
    Read-Host "Nhan Enter de thoat"
    exit 1
}

# Ham kiem tra file HTML co bi hu khong
function Test-HTMLIntegrity {
    param([string]$FilePath)
    
    try {
        $content = Get-Content -Path $FilePath -Raw -Encoding UTF8 -ErrorAction Stop
        
        # Kiem tra co rong khong
        if ([string]::IsNullOrWhiteSpace($content)) {
            return @{ IsValid = $false; Reason = "File rong" }
        }
        
        # Kiem tra co DOCTYPE khong
        if ($content -notmatch '<!DOCTYPE\s+html>') {
            return @{ IsValid = $false; Reason = "Thieu DOCTYPE" }
        }
        
        # Kiem tra co the html khong
        if ($content -notmatch '<html[^>]*>') {
            return @{ IsValid = $false; Reason = "Thieu the <html>" }
        }
        
        # Kiem tra co the head khong
        if ($content -notmatch '<head[^>]*>') {
            return @{ IsValid = $false; Reason = "Thieu the <head>" }
        }
        
        # Kiem tra co the body khong
        if ($content -notmatch '<body[^>]*>') {
            return @{ IsValid = $false; Reason = "Thieu the <body>" }
        }
        
        # Kiem tra co dong the html khong
        if ($content -notmatch '</html>') {
            return @{ IsValid = $false; Reason = "Thieu the dong </html>" }
        }
        
        # Kiem tra co dong the head khong
        if ($content -notmatch '</head>') {
            return @{ IsValid = $false; Reason = "Thieu the dong </head>" }
        }
        
        # Kiem tra co dong the body khong
        if ($content -notmatch '</body>') {
            return @{ IsValid = $false; Reason = "Thieu the dong </body>" }
        }
        
        # Kiem tra co title khong
        if ($content -notmatch '<title[^>]*>.*?</title>') {
            return @{ IsValid = $false; Reason = "Thieu the <title>" }
        }
        
        # Kiem tra co charset khong
        if ($content -notmatch 'charset=') {
            return @{ IsValid = $false; Reason = "Thieu charset" }
        }
        
        # Kiem tra file qua nho (duoi 500 bytes co the bi hu)
        if ($content.Length -lt 500) {
            return @{ IsValid = $false; Reason = "File qua nho ($($content.Length) bytes)" }
        }
        
        # Kiem tra co noi dung chinh khong (khong chi la template)
        $bodyContent = ""
        if ($content -match '(?s)<body[^>]*>(.*?)</body>') {
            $bodyContent = $matches[1]
            # Xoa cac the script va style de kiem tra noi dung thuc
            $bodyContent = $bodyContent -replace '(?s)<script[^>]*>.*?</script>', ''
            $bodyContent = $bodyContent -replace '(?s)<style[^>]*>.*?</style>', ''
            $bodyContent = $bodyContent -replace '<[^>]+>', ''
            $bodyContent = $bodyContent.Trim()
            
            if ($bodyContent.Length -lt 50) {
                return @{ IsValid = $false; Reason = "Khong co noi dung chinh" }
            }
        }
        
        # Kiem tra co loi cu phap HTML nghiem trong khong
        $openTags = ($content | Select-String -Pattern '<(\w+)[^>]*>' -AllMatches).Matches.Count
        $closeTags = ($content | Select-String -Pattern '</(\w+)>' -AllMatches).Matches.Count
        
        # Cho phep chenh lech 20% (vi co the co self-closing tags)
        $tagDifference = [Math]::Abs($openTags - $closeTags)
        $allowedDifference = [Math]::Max(10, $openTags * 0.2)
        
        if ($tagDifference -gt $allowedDifference) {
            return @{ IsValid = $false; Reason = "The HTML khong can bang (mo: $openTags, dong: $closeTags)" }
        }
        
        return @{ IsValid = $true; Reason = "File hop le" }
    }
    catch {
        return @{ IsValid = $false; Reason = "Loi doc file: $($_.Exception.Message)" }
    }
}

# Tim tat ca file HTML
$htmlFiles = Get-ChildItem -Path $Path -Recurse -Filter "*.html" | Where-Object { $_.Name -notlike "*backup*" }
Write-Host "Tim thay $($htmlFiles.Count) file HTML" -ForegroundColor Cyan
Write-Host ""

if ($htmlFiles.Count -eq 0) {
    Write-Host "Khong tim thay file HTML nao" -ForegroundColor Yellow
    Read-Host "Nhan Enter de thoat"
    exit 0
}

$validFiles = @()
$brokenFiles = @()
$totalSize = 0

Write-Host "Dang kiem tra file..." -ForegroundColor Yellow
Write-Host ""

foreach ($file in $htmlFiles) {
    $result = Test-HTMLIntegrity -FilePath $file.FullName
    $fileSize = [math]::Round($file.Length / 1KB, 2)
    $totalSize += $file.Length
    
    if ($result.IsValid) {
        $validFiles += $file
        if ($Verbose) {
            Write-Host "OK  : $($file.Name) ($fileSize KB)" -ForegroundColor Green
        }
    } else {
        $brokenFiles += @{
            File = $file
            Reason = $result.Reason
            Size = $fileSize
        }
        Write-Host "BAD : $($file.Name) ($fileSize KB) - $($result.Reason)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== KET QUA KIEM TRA ===" -ForegroundColor Green
Write-Host "Tong file HTML: $($htmlFiles.Count)" -ForegroundColor Cyan
Write-Host "File hop le: $($validFiles.Count)" -ForegroundColor Green
Write-Host "File bi hu: $($brokenFiles.Count)" -ForegroundColor Red
Write-Host "Tong dung luong: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Cyan

if ($brokenFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "=== DANH SACH FILE BI HU ===" -ForegroundColor Red
    
    $brokenSize = 0
    foreach ($broken in $brokenFiles) {
        Write-Host "- $($broken.File.Name) ($($broken.Size) KB) - $($broken.Reason)" -ForegroundColor Yellow
        $brokenSize += $broken.File.Length
    }
    
    Write-Host ""
    Write-Host "Dung luong file bi hu: $([math]::Round($brokenSize / 1KB, 2)) KB" -ForegroundColor Yellow
    
    if (-not $DryRun) {
        Write-Host ""
        $confirm = Read-Host "Ban co muon XOA $($brokenFiles.Count) file bi hu? (y/n)"
        
        if ($confirm -eq 'y' -or $confirm -eq 'Y') {
            Write-Host ""
            Write-Host "Dang xoa file bi hu..." -ForegroundColor Red
            
            $deletedCount = 0
            $deletedSize = 0
            
            foreach ($broken in $brokenFiles) {
                try {
                    $fileSize = $broken.File.Length
                    Remove-Item $broken.File.FullName -Force
                    Write-Host "Da xoa: $($broken.File.Name)" -ForegroundColor Red
                    $deletedCount++
                    $deletedSize += $fileSize
                }
                catch {
                    Write-Host "Loi xoa: $($broken.File.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
            
            Write-Host ""
            Write-Host "=== KET QUA XOA ===" -ForegroundColor Green
            Write-Host "Da xoa: $deletedCount file" -ForegroundColor Red
            Write-Host "Tiet kiem: $([math]::Round($deletedSize / 1KB, 2)) KB" -ForegroundColor Green
            Write-Host "Con lai: $($validFiles.Count) file hop le" -ForegroundColor Green
        } else {
            Write-Host "Da huy bo viec xoa" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "CHE DO DRY RUN - Khong xoa file nao" -ForegroundColor Yellow
        Write-Host "De xoa that, chay lai khong co tham so -DryRun" -ForegroundColor Cyan
    }
} else {
    Write-Host ""
    Write-Host "Tat ca file HTML deu hop le!" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== THONG KE CUOI ===" -ForegroundColor Green
Write-Host "File hop le: $($validFiles.Count)" -ForegroundColor Green
Write-Host "File bi hu: $($brokenFiles.Count)" -ForegroundColor $(if($brokenFiles.Count -gt 0){"Red"}else{"Green"})

Write-Host ""
Write-Host "Hoan thanh!" -ForegroundColor Green
Read-Host "Nhan Enter de thoat"
