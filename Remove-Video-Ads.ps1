# Script PowerShell để xóa quảng cáo video player
# <PERSON><PERSON><PERSON> g<PERSON>: AI Assistant

param(
    [string]$Path = "heovl.fit",
    [switch]$Backup = $true
)

Write-Host "=== XOA QUANG CAO VIDEO PLAYER ===" -ForegroundColor Green
Write-Host "Dang xu ly thu muc: $Path" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $Path)) {
    Write-Host "Khong tim thay thu muc: $Path" -ForegroundColor Red
    Read-Host "Nhan Enter de thoat"
    exit 1
}

# Ham tao backup
function Create-Backup {
    param([string]$FilePath)
    if ($Backup) {
        $backupPath = $FilePath + ".backup_video"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $FilePath $backupPath
        }
    }
}

# Ham xoa ads tu video URLs
function Remove-VideoAds {
    param([string]$Content)
    
    Write-Host "Dang xoa quang cao video..." -ForegroundColor Yellow
    
    # 1. Xoa adTag parameters tu data-source URLs
    $Content = $Content -replace 'data-source="([^"]*?)&amp;adTag=[^"]*"', 'data-source="$1"'
    $Content = $Content -replace 'data-source="([^"]*?)\?adTag=[^"]*"', 'data-source="$1"'
    $Content = $Content -replace 'data-source="([^"]*?)&adTag=[^"]*"', 'data-source="$1"'
    
    # 2. Sua function setPlayerSource de loai bo ads
    $oldPlayerFunction = @'
function setPlayerSource\(url\) \{
            document\.querySelectorAll\('\.set-player-source'\)\.forEach\(e => e\.classList\.remove\('video-player__cdn-selector-button--active'\) \|\| e\.classList\.add\('cdn-selector-button--inactive'\)\)
            const activeBtn = document\.querySelector\('\.set-player-source\[data-source="\' \+ url \+ \'"\]'\)
            activeBtn\.setAttribute\('data-active', 'true'\)
            activeBtn\.classList\.add\('video-player__cdn-selector-button--active'\)
            activeBtn\.classList\.remove\('cdn-selector-button--inactive'\)

            const playerWrapper = document\.querySelector\('\#player-wrapper'\)
            if \(\!url && message\) \{
                playerWrapper\.innerHTML = message
                return
            \}

            playerWrapper\.innerHTML = `<iframe src="\` \+ url \+ `" style="border: 0; width: 100%; height: 100%;" frameborder="0" scrolling="0" allowfullscreen></iframe>`
        \}
'@

    $newPlayerFunction = @'
function setPlayerSource(url) {
            document.querySelectorAll('.set-player-source').forEach(e => e.classList.remove('video-player__cdn-selector-button--active') || e.classList.add('cdn-selector-button--inactive'))
            const activeBtn = document.querySelector('.set-player-source[data-source="' + url + '"]')
            activeBtn.setAttribute('data-active', 'true')
            activeBtn.classList.add('video-player__cdn-selector-button--active')
            activeBtn.classList.remove('cdn-selector-button--inactive')

            const playerWrapper = document.querySelector('#player-wrapper')
            if (!url && message) {
                playerWrapper.innerHTML = message
                return
            }

            // Remove ads parameters from URL
            let cleanUrl = url;
            if (cleanUrl.includes('adTag=')) {
                cleanUrl = cleanUrl.split('&adTag=')[0];
            }
            if (cleanUrl.includes('?adTag=')) {
                cleanUrl = cleanUrl.split('?adTag=')[0];
            }
            
            console.log('Loading video without ads:', cleanUrl);
            playerWrapper.innerHTML = `<iframe src="` + cleanUrl + `" style="border: 0; width: 100%; height: 100%;" frameborder="0" scrolling="0" allowfullscreen></iframe>`
        }
'@

    $Content = $Content -replace $oldPlayerFunction, $newPlayerFunction
    
    # 3. Them script chan ads trong iframe (neu chua co)
    if ($Content -notmatch "Video player ads blocked") {
        $videoAdsBlockScript = @'
    // Additional video player ads blocking
    setTimeout(function() {
        const iframe = document.querySelector('#player-wrapper iframe');
        if (iframe) {
            // Block ads in video player iframe
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        // Remove ads elements from video player
                        const adsSelectors = [
                            '[class*="ad"]',
                            '[id*="ad"]',
                            '[class*="banner"]',
                            '[id*="banner"]',
                            '.vast-ad',
                            '.video-ad',
                            '.preroll'
                        ];
                        
                        adsSelectors.forEach(selector => {
                            const adsElements = iframeDoc.querySelectorAll(selector);
                            adsElements.forEach(el => el.remove());
                        });
                        
                        console.log('Video player ads blocked');
                    }
                } catch (e) {
                    console.log('Cross-origin iframe - cannot access content');
                }
            });
        }
    }, 2000);
'@
        
        $Content = $Content -replace "(console\.log\('Website cleaned[^']*'\);)", "`$1`n$videoAdsBlockScript"
    }
    
    return $Content
}

# Tim tat ca file HTML video
$videoFiles = Get-ChildItem -Path (Join-Path $Path "videos") -Filter "*.html" -ErrorAction SilentlyContinue
if (-not $videoFiles) {
    Write-Host "Khong tim thay file video nao" -ForegroundColor Yellow
    Read-Host "Nhan Enter de thoat"
    exit 0
}

Write-Host "Tim thay $($videoFiles.Count) file video" -ForegroundColor Cyan
Write-Host ""

$processedCount = 0
$errorCount = 0

foreach ($file in $videoFiles) {
    try {
        Write-Host "Dang xu ly: $($file.Name)" -ForegroundColor Yellow
        
        # Tao backup
        Create-Backup -FilePath $file.FullName
        
        # Doc va sua noi dung
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Kiem tra co video player khong
        if ($content -match "data-source.*streamqq" -or $content -match "setPlayerSource") {
            $cleanContent = Remove-VideoAds -Content $content
            
            # Ghi lai file
            Set-Content -Path $file.FullName -Value $cleanContent -Encoding UTF8
            
            Write-Host "Da xoa ads video: $($file.Name)" -ForegroundColor Green
            $processedCount++
        } else {
            Write-Host "Khong co video player: $($file.Name)" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "Loi: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host ""
Write-Host "=== KET QUA ===" -ForegroundColor Green
Write-Host "Da xu ly: $processedCount file video" -ForegroundColor Cyan
Write-Host "Loi: $errorCount file" -ForegroundColor $(if($errorCount -gt 0){"Red"}else{"Cyan"})

if ($Backup) {
    Write-Host "File backup: *.backup_video" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== HUONG DAN ===" -ForegroundColor Green
Write-Host "1. Refresh trang video trong trinh duyet" -ForegroundColor White
Write-Host "2. Bam play video - se khong con quang cao" -ForegroundColor White
Write-Host "3. Kiem tra Console (F12) de xem log" -ForegroundColor White
Write-Host "4. Neu van co ads, co the do cross-origin policy" -ForegroundColor White

Write-Host ""
Write-Host "Hoan thanh!" -ForegroundColor Green
Read-Host "Nhan Enter de thoat"
