(function () {
  const CONFIG = ADS_CONFIG.topBanner;
  const TOP_BANNER_KEY = "lastTopBannerCloseTime";

  if (!CONFIG.enabled) return; 

  function injectCSS() {
    const css = `
      #topBannerContainer {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        display: none; /* Mặc định ẩn */
        justify-content: center;
        z-index: 999;
      }
      .top-banner-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px; /* Khoảng cách giữa hai banner */
        position: relative;
      }
      .top-banner-item {
        display: block;
        height: 35px;
        aspect-ratio: 728/90;
        margin: 0 auto;
        object-fit: cover;
        max-width: 728px;
      }
      @media screen and (min-width: 768px) {
        .top-banner-item {
          height: 50px;
          max-height: 80px; /* <PERSON><PERSON><PERSON> thước giống design ban đầu */
        }
      }
      .top-banner-close-btn {
        position: absolute;
        bottom: -20px; /* Đặt nút gần sát với vùng chứa banner */
        right: -20px; /* Đặt nút ở góc phải */
        background-color: #f2f2f2;
        color: red;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s ease;
      }
      .top-banner-close-btn:hover {
        background-color: #e0e0e0;
      }
    `;
    const style = document.createElement("style");
    style.textContent = css;
    document.head.appendChild(style);
  }

  function createTopBanner() {
    const topBanner = document.createElement("div");
    topBanner.id = "topBannerContainer";
    topBanner.className = "overlay-ad";

    const bannerWrapper = document.createElement("div");
    bannerWrapper.classList.add("overlay-ad");
    bannerWrapper.className = "top-banner-wrapper";

    CONFIG.banners.forEach((ad) => {
      const banner = document.createElement("img");
	  banner.setAttribute("loading", "lazy");
      banner.src = ad.img;
      banner.alt = "Top Banner";
      banner.className = "top-banner-item";
      banner.onclick = () => window.open(ad.link);
      bannerWrapper.appendChild(banner);
    });

    const closeButton = document.createElement("button");
    closeButton.innerHTML = "×";
    closeButton.className = "top-banner-close-btn";
    closeButton.classList.add("overlay-ad");
    closeButton.onclick = () => {
      topBanner.style.display = "none";
      saveCloseTimeToLocalStorage(TOP_BANNER_KEY);
      console.log("Top banner ads closed.");
    };

    bannerWrapper.appendChild(closeButton);
    topBanner.appendChild(bannerWrapper);
    document.body.appendChild(topBanner);
  }

  injectCSS();
  createTopBanner();
  showAdsIfIntervalPassed(TOP_BANNER_KEY, CONFIG.interval, "topBannerContainer");
})();