function isCountryVn() {
  const country = localStorage.getItem("userCountry");
  return country == "vn";
}

function saveCloseTimeToLocalStorage(key) {
  const currentTime = Date.now();
  localStorage.setItem(key, currentTime);
}

function getCloseTimeFromLocalStorage(key) {
  return parseInt(localStorage.getItem(key)) || 0;
}

function showAdsIfIntervalPassed(key, interval, containerId) {
  const lastCloseTime = getCloseTimeFromLocalStorage(key);
  const currentTime = Date.now();
  if (isCountryVn() && currentTime - lastCloseTime >= interval) {
    document.getElementById(containerId).style.display = "flex";
  }
}